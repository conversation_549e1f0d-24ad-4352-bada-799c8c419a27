<template>
  <div class="chat4-landing-page">
    <!-- Particle Background -->
    <Chat4ParticleBackground />

    <!-- Main Content -->
    <div class="landing-content">
      <!-- Header Section -->
      <header class="landing-header">
        <div class="logo-container">
          <img
            src="https://static.playshot.ai/static/images/logo/reelplay_logo.png"
            alt="ReelPlay Logo"
            class="main-logo"
          />
        </div>

        <div class="hero-section">
          <h1 class="hero-title">
            Experience Next-Gen
            <span class="gradient-text">Interactive Entertainment</span>
          </h1>
          <p class="hero-subtitle">
            Immerse yourself in live streaming, real-time chat, and interactive
            experiences
          </p>
        </div>
      </header>

      <!-- Characters Grid -->
      <section class="characters-section">
        <div class="section-header">
          <h2 class="section-title">Choose Your Experience</h2>
          <p class="section-subtitle"
            >Click any character to start your interactive journey</p
          >
        </div>

        <div class="characters-grid" v-if="availableStories.length > 0">
          <Chat4CharacterCard
            v-for="story in availableStories"
            :key="story.id"
            :story="story"
            :character="story.actors[0]"
            @click="handleCharacterClick"
            class="character-card"
          />
        </div>

        <!-- Loading State -->
        <div v-else-if="isLoading" class="loading-state">
          <div class="loading-spinner"></div>
          <p class="loading-text">Loading experiences...</p>
        </div>

        <!-- Empty State -->
        <div v-else class="empty-state">
          <p class="empty-text">No experiences available at the moment</p>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStoryStore, useUserStore } from '@/store'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import Chat4ParticleBackground from './components/Chat4ParticleBackground.vue'
import Chat4CharacterCard from './components/Chat4CharacterCard.vue'

const router = useRouter()
const storyStore = useStoryStore()
const userStore = useUserStore()

const isLoading = ref(true)

// Filter stories that support chat4 mode
const availableStories = computed(() => {
  return storyStore.stories.filter((story) => {
    // Add logic here to filter stories that support chat4
    // For now, return all stories
    return story.actors && story.actors.length > 0
  })
})

const handleCharacterClick = (story: any, character: any) => {
  // Report analytics event
  reportEvent(ReportEvent.StartChat, {
    userId: userStore.userInfo?.uuid,
    character: character.name,
    storyId: story.id,
    actorId: character.id,
    buttonType: 'chat4LandingCard',
    chatMode: 'chat4',
  })

  // Navigate to chat4 with the selected story/character
  router.push(`/chat4/${story.id}/${character.id}`)
}

const initializePage = async () => {
  try {
    isLoading.value = true

    // Fetch stories if not already loaded
    if (storyStore.stories.length === 0) {
      await storyStore.fetchStories()
    }

    // Report page visit
    reportEvent(ReportEvent.VisitLandingPage, {
      userId: userStore.userInfo?.uuid,
      landingPageType: 'chat4',
      availableStories: availableStories.value.length,
    })
  } catch (error) {
    console.error('Failed to initialize chat4 landing page:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  initializePage()
})
</script>

<style lang="less" scoped>
@import '@/assets/style/mixin.less';

// Import chat4 CSS variables
:root {
  --chat4-primary-color: #ff004d;
  --chat4-secondary-color: #4fc3f7;
  --chat4-accent-color: #ffd700;
  --chat4-success-color: #4caf50;
  --chat4-warning-color: #ff9800;
  --chat4-error-color: #f44336;

  // 背景颜色
  --chat4-bg-primary: #000000;
  --chat4-bg-secondary: #1a1a1a;
  --chat4-bg-overlay: rgba(0, 0, 0, 0.5);
  --chat4-bg-glass: rgba(255, 255, 255, 0.1);

  // 文字颜色
  --chat4-text-primary: #ffffff;
  --chat4-text-secondary: rgba(255, 255, 255, 0.8);
  --chat4-text-muted: rgba(255, 255, 255, 0.6);

  // 边框和阴影
  --chat4-border-color: rgba(255, 255, 255, 0.2);
  --chat4-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --chat4-shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.2);
  --chat4-shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.3);

  // 渐变
  --chat4-gradient-primary: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  --chat4-gradient-secondary: linear-gradient(135deg, #4fc3f7, #29b6f6);
  --chat4-gradient-accent: linear-gradient(135deg, #ffd700, #ffed4e);
  --chat4-gradient-glass: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );

  // 动画时长
  --chat4-transition-fast: 0.2s;
  --chat4-transition-normal: 0.3s;
  --chat4-transition-slow: 0.5s;

  // 圆角
  --chat4-radius-small: 8px;
  --chat4-radius-medium: 12px;
  --chat4-radius-large: 20px;
  --chat4-radius-round: 50%;

  // 间距
  --chat4-spacing-xs: 4px;
  --chat4-spacing-sm: 8px;
  --chat4-spacing-md: 16px;
  --chat4-spacing-lg: 24px;
  --chat4-spacing-xl: 32px;
}

.chat4-landing-page {
  min-height: calc(var(--vh, 1vh) * 100);
  background: var(--chat4-bg-primary);
  color: var(--chat4-text-primary);
  position: relative;
  overflow-x: hidden;
}

.landing-content {
  position: relative;
  z-index: 2;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.landing-header {
  text-align: center;
  margin-bottom: 60px;
}

.logo-container {
  margin-bottom: 40px;

  .main-logo {
    height: 60px;
    width: auto;
    object-fit: contain;
  }
}

.hero-section {
  .hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;
    color: var(--chat4-text-primary);

    .gradient-text {
      background: var(--chat4-gradient-primary);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .hero-subtitle {
    font-size: 1.2rem;
    color: var(--chat4-text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

.characters-section {
  .section-header {
    text-align: center;
    margin-bottom: 40px;

    .section-title {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 12px;
      color: var(--chat4-text-primary);
    }

    .section-subtitle {
      font-size: 1rem;
      color: var(--chat4-text-secondary);
    }
  }
}

.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--chat4-border-color);
    border-radius: 50%;
    border-top-color: var(--chat4-primary-color);
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }

  .loading-text,
  .empty-text {
    color: var(--chat4-text-secondary);
    font-size: 1rem;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .landing-content {
    padding: 16px;
  }

  .hero-section {
    .hero-title {
      font-size: 2rem;
    }

    .hero-subtitle {
      font-size: 1rem;
    }
  }

  .characters-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .section-header {
    .section-title {
      font-size: 1.5rem;
    }
  }
}

@media (max-width: 480px) {
  .characters-grid {
    grid-template-columns: 1fr;
  }

  .hero-section {
    .hero-title {
      font-size: 1.8rem;
    }
  }
}
</style>
