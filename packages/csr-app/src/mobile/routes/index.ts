import type { RouteRecordRaw } from 'vue-router'
import { landingRoutes, landingRoutes2 } from './landing'
import creationRoutes from './creation'
import chat4LandingRoutes from './chat4-landing'
import { useChatUIStore } from '@/store/chat-ui'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useChatEventsStore } from '@/store/chat-events'
import { useStoryStore } from '@/store/story'
import { createRouteLoader } from '@/utils/advanced-route-loader'

// 支付成功标记，用于验证是否可以访问支付成功页面
let isPaymentSuccessful = false

// 设置支付成功标记的方法
export const setPaymentSuccessful = () => {
  isPaymentSuccessful = true
}

const mobileRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    component: createRouteLoader(
      () => import('../components/MobileLayout.vue'),
      'mobile-layout',
      {
        preload: true,
        priority: 'high',
      },
    ),
    children: [
      {
        path: '/',
        name: 'Stories',
        component: () => import('../views/stories/index.vue'),
        meta: {
          title: 'Stories',
          requiresAuth: false,
          showMenu: true,
          seo: {
            dynamic: true,
          },
        },
      },
      ...landingRoutes,
      ...landingRoutes2,
      ...creationRoutes,
      ...chat4LandingRoutes,
      {
        path: '/story/:storyId',
        name: 'StoryIntro',
        component: createRouteLoader(
          () => import('../views/chat/components/StoryIntro.vue'),
          'story-intro',
          { priority: 'high' },
        ),
        meta: {
          requiresAuth: true,
          showMenu: false,
          seo: {
            dynamic: true,
          },
        },
      },
      {
        path: '/user-character',
        name: 'UserCharacter',
        component: createRouteLoader(
          () => import('../views/user-character/index.vue'),
          'user-character',
          { priority: 'normal' },
        ),
        meta: {
          requiresAuth: true,
          requiresLogin: true,
          showMenu: true,
        },
      },
      {
        path: '/chat/:storyId/:actorId',
        name: 'Chat',
        component: createRouteLoader(
          () => import('../views/chat/index.vue'),
          'chat',
          {
            priority: 'high',
            timeout: 20000,
          },
        ),
        meta: {
          requiresAuth: true,
          showMenu: false,
          transition: 'fade',
          version: 'legacy',
          seo: {
            dynamic: true,
          },
        },
        beforeEnter: async (to, from, next) => {
          const chatUIStore = useChatUIStore()
          const chatResourcesStore = useChatResourcesStore()
          const chatEventsStore = useChatEventsStore()
          const storyStore = useStoryStore()

          if (from.name === 'SocialCallback') {
            chatUIStore.isRedirectToChat = true
          }
          chatResourcesStore.isFirstVideo = !from.name
          if (!from.name) {
            // 表示是通过刷新或者直接输入url访问的
            chatEventsStore.setShouldRestart(false)

            // 直接URL访问时，需要根据路由参数设置当前故事和角色
            const storyId = to.params.storyId as string
            const actorId = to.params.actorId as string

            if (
              storyId &&
              (!storyStore.currentStory ||
                storyStore.currentStory.id !== storyId)
            ) {
              try {
                // 获取故事详情并设置当前故事
                const storyDetail = await storyStore.getStoreDetail(storyId)
                if (storyDetail?.story) {
                  // 根据actorId找到对应的角色并设置
                  const actor = storyDetail.story.actors?.find(
                    (a) => a.id === actorId,
                  )
                  if (actor) {
                    storyStore.setCurrentActor(actor)
                  }
                }
              } catch (error) {
                console.error(
                  'Failed to load story detail in route guard:',
                  error,
                )
              }
            }
          }
          next()
        },
      },
      {
        path: '/chat2/:storyId/:actorId',
        name: 'Chat2',
        component: createRouteLoader(
          () => import('../views/chat2/index.vue'),
          'chat2',
          {
            priority: 'high',
            timeout: 20000,
          },
        ),
        meta: {
          requiresAuth: true,
          showMenu: false,
          transition: 'fade',
          version: 'new',
          seo: {
            dynamic: true,
          },
        },
        beforeEnter: async (to, from, next) => {
          const chatUIStore = useChatUIStore()
          const chatResourcesStore = useChatResourcesStore()
          const chatEventsStore = useChatEventsStore()
          const storyStore = useStoryStore()

          if (from.name === 'SocialCallback') {
            chatUIStore.isRedirectToChat = true
          }
          chatResourcesStore.isFirstVideo = !from.name
          if (!from.name) {
            // 表示是通过刷新或者直接输入url访问的
            chatEventsStore.setShouldRestart(false)

            // 直接URL访问时，需要根据路由参数设置当前故事和角色
            const storyId = to.params.storyId as string
            const actorId = to.params.actorId as string

            if (
              storyId &&
              (!storyStore.currentStory ||
                storyStore.currentStory.id !== storyId)
            ) {
              try {
                // 获取故事详情并设置当前故事
                const storyDetail = await storyStore.getStoreDetail(storyId)
                if (storyDetail?.story) {
                  // 根据actorId找到对应的角色并设置
                  const actor = storyDetail.story.actors?.find(
                    (a) => a.id === actorId,
                  )
                  if (actor) {
                    storyStore.setCurrentActor(actor)
                  }
                }
              } catch (error) {
                console.error(
                  'Failed to load story detail in route guard:',
                  error,
                )
              }
            }
          }
          next()
        },
      },
      {
        path: '/chat3/:actorId/:storyId',
        name: 'Chat3',
        component: createRouteLoader(
          () => import('../views/chat3/index.vue'),
          'chat3',
          {
            priority: 'high',
            timeout: 20000,
          },
        ),
        meta: {
          requiresAuth: true,
          showMenu: false,
          transition: 'fade',
          version: 'new',
        },
        beforeEnter: async (to, from, next) => {
          const chatUIStore = useChatUIStore()
          const chatResourcesStore = useChatResourcesStore()
          const chatEventsStore = useChatEventsStore()
          const storyStore = useStoryStore()

          if (from.name === 'SocialCallback') {
            chatUIStore.isRedirectToChat = true
          }
          chatResourcesStore.isFirstVideo = !from.name
          if (!from.name) {
            // 表示是通过刷新或者直接输入url访问的
            chatEventsStore.setShouldRestart(false)

            // 直接URL访问时，需要根据路由参数设置当前故事和角色
            // 注意：Chat3的参数顺序是 /:actorId/:storyId
            const actorId = to.params.actorId as string
            const storyId = to.params.storyId as string

            if (
              storyId &&
              (!storyStore.currentStory ||
                storyStore.currentStory.id !== storyId)
            ) {
              try {
                // 获取故事详情并设置当前故事
                const storyDetail = await storyStore.getStoreDetail(storyId)
                if (storyDetail?.story) {
                  // 根据actorId找到对应的角色并设置
                  const actor = storyDetail.story.actors?.find(
                    (a) => a.id === actorId,
                  )
                  if (actor) {
                    storyStore.setCurrentActor(actor)
                  }
                }
              } catch (error) {
                console.error(
                  'Failed to load story detail in route guard:',
                  error,
                )
              }
            }
          }
          next()
        },
      },
      {
        path: '/chat4/:storyId/:actorId',
        name: 'Chat4',
        component: createRouteLoader(
          () => import('../views/chat4/index.vue'),
          'chat4',
          {
            priority: 'high',
            timeout: 20000,
          },
        ),
        meta: {
          requiresAuth: true,
          showMenu: false,
          transition: 'fade',
          version: 'live',
          seo: {
            dynamic: true,
          },
        },
        beforeEnter: async (to, from, next) => {
          const chatUIStore = useChatUIStore()
          const chatResourcesStore = useChatResourcesStore()
          const chatEventsStore = useChatEventsStore()
          const storyStore = useStoryStore()

          if (from.name === 'SocialCallback') {
            chatUIStore.isRedirectToChat = true
          }
          chatResourcesStore.isFirstVideo = !from.name
          if (!from.name) {
            // 表示是通过刷新或者直接输入url访问的
            chatEventsStore.setShouldRestart(false)

            // 直接URL访问时，需要根据路由参数设置当前故事和角色
            const storyId = to.params.storyId as string
            const actorId = to.params.actorId as string

            if (
              storyId &&
              (!storyStore.currentStory ||
                storyStore.currentStory.id !== storyId)
            ) {
              try {
                // 获取故事详情并设置当前故事
                const storyDetail = await storyStore.getStoreDetail(storyId)
                if (storyDetail?.story) {
                  // 根据actorId找到对应的角色并设置
                  const actor = storyDetail.story.actors?.find(
                    (a) => a.id === actorId,
                  )
                  if (actor) {
                    storyStore.setCurrentActor(actor)
                  }
                }
              } catch (error) {
                console.error(
                  'Failed to load story detail in route guard:',
                  error,
                )
              }
            }
          }
          next()
        },
      },
      {
        path: '/user/login',
        name: 'UserLogin',
        component: createRouteLoader(
          () => import('../views/user/login.vue'),
          'user-login',
          {
            preload: true,
            priority: 'high',
          },
        ),
        meta: {
          requiresAuth: false,
          showMenu: false,
          seo: {
            pageType: 'login',
          },
        },
      },
      {
        path: '/user/register',
        name: 'UserRegister',
        component: createRouteLoader(
          () => import('../views/user/register.vue'),
          'user-register',
          {
            priority: 'normal',
          },
        ),
        meta: {
          requiresAuth: false,
          showMenu: false,
          seo: {
            pageType: 'signup',
          },
        },
      },
      {
        path: '/user/social-callback',
        name: 'SocialCallback',
        component: createRouteLoader(
          () => import('../views/user/SocialCallback.vue'),
          'social-callback',
          { priority: 'low' },
        ),
        meta: {
          requiresAuth: false,
          showMenu: false,
        },
      },
      {
        path: '/user/profile',
        name: 'UserProfile',
        component: createRouteLoader(
          () => import('../views/user/profile.vue'),
          'user-profile',
          {
            priority: 'normal',
          },
        ),
        beforeEnter: async (to, from, next) => {
          const { useUserStore } = await import('@/store')
          const userStore = useUserStore()
          if (userStore.isGuest) {
            next('/user/login')
            return
          }
          next()
        },
        meta: {
          requiresAuth: true,
          showMenu: true,
          seo: {
            pageType: 'profile',
          },
        },
      },
      {
        path: '/user/settings',
        name: 'UserSettings',
        component: () => import('../views/user/settings.vue'),
        meta: {
          requiresAuth: true,
          showMenu: false,
          seo: {
            pageType: 'settings',
          },
        },
      },
      {
        path: '/user/coins',
        name: 'UserCoins',
        component: () => import('../views/user/coins.vue'),
        meta: {
          requiresAuth: true,
        },
      },
      {
        path: '/terms',
        name: 'Terms',
        component: () => import('../views/terms/index.vue'),
        meta: {
          requiresAuth: false,
          showMenu: false,
          seo: {
            pageType: 'terms',
          },
        },
      },
      {
        path: '/privacy',
        name: 'Privacy',
        component: () => import('../views/privacy/index.vue'),
        meta: {
          requiresAuth: false,
          showMenu: false,
          seo: {
            pageType: 'privacy',
          },
        },
      },
      {
        path: '/complaints',
        name: 'Complaints',
        component: () => import('../views/complaints/index.vue'),
        meta: {
          requiresAuth: false,
          showMenu: false,
          seo: {
            pageType: 'complaints',
          },
        },
      },
      {
        path: '/content-removal',
        name: 'ContentRemoval',
        component: () => import('../views/content-removal/index.vue'),
        meta: {
          requiresAuth: false,
          showMenu: false,
          seo: {
            pageType: 'content-removal',
          },
        },
      },
      {
        path: '/record-keeping',
        name: 'RecordKeeping',
        component: () => import('../views/record-keeping/index.vue'),
        meta: {
          requiresAuth: false,
          showMenu: false,
          seo: {
            pageType: 'record-keeping',
          },
        },
      },
      {
        path: '/about',
        name: 'About',
        component: () => import('../views/about/index.vue'),
        meta: {
          requiresAuth: false,
          showMenu: false,
          seo: {
            pageType: 'about',
          },
        },
      },
      {
        path: '/refund',
        name: 'Refund',
        component: () => import('../views/refund/index.vue'),
        meta: {
          requiresAuth: false,
          showMenu: false,
          seo: {
            pageType: 'refund',
          },
        },
      },
      {
        path: '/recharge-success',
        name: 'RechargeSuccess',
        component: () => import('../views/recharge-success/index.vue'),
        meta: {
          requiresAuth: true,
          showMenu: false,
        },
        beforeEnter: (to, from, next) => {
          // 只允许从支付流程来的访问，或者有支付成功标记的访问
          if (isPaymentSuccessful || from.path.includes('/payment')) {
            isPaymentSuccessful = false // 重置标记
            next()
          } else {
            next('/') // 否则重定向到首页
          }
        },
      },
      {
        path: '/payment/stripe-callback',
        name: 'StripeCallback',
        component: () => import('../views/payment/stripe-callback.vue'),
        meta: {
          requiresAuth: true,
          showMenu: false,
        },
      },
      {
        path: '/daily-tasks',
        name: 'DailyTasks',
        component: () => import('../views/tasks/index.vue'),
        meta: {
          requiresAuth: true,
          requiresLogin: true,
          showMenu: false,
          title: 'Daily Tasks',
        },
      },
      {
        path: '/region-restricted',
        name: 'RegionRestricted',
        component: () => import('../views/region-restricted/index.vue'),
        meta: {
          requiresAuth: false,
          showMenu: false,
          title: 'Service Unavailable',
        },
      },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/',
  },
]

export default mobileRoutes
