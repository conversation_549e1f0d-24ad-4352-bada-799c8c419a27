import { defineStore } from 'pinia'
import { ref, readonly } from 'vue'
import {
  createPaymentAPI,
  type PriceItem,
  getProjectConfig,
  isPlayshot,
} from 'shared-payment'

export const useRechargeStore = defineStore(
  'recharge',
  () => {
    // 创建支付API实例
    const paymentAPI = createPaymentAPI(
      import.meta.env.VITE_API_HOST,
      () => localStorage.getItem('token') || '',
    )

    // State
    const visible = ref(false)
    const priceList = ref<PriceItem[]>([])
    const loading = ref(false)
    const error = ref<string | null>(null)
    const paymentLoading = ref(false)

    // 获取项目配置
    const projectConfig = getProjectConfig()

    // Actions
    const fetchPriceList = async () => {
      loading.value = true
      error.value = null

      try {
        const data = await paymentAPI.getPriceList()
        priceList.value = data
      } catch (err) {
        error.value =
          err instanceof Error ? err.message : 'Failed to fetch price list'
        throw err
      } finally {
        loading.value = false
      }
    }

    /**
     * 创建支付订单
     */
    const createPayment = async (priceId: string): Promise<PaymentResult> => {
      paymentLoading.value = true
      error.value = null

      try {
        const result = await paymentAPI.createPayment({
          priceId,
          successUrl: generateSuccessUrl(),
          cancelUrl: generateCancelUrl(),
        })

        return result
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Payment creation failed'
        error.value = errorMessage
        throw new Error(errorMessage)
      } finally {
        paymentLoading.value = false
      }
    }

    /**
     * 创建支付并跳转
     */
    const createAndRedirectToPayment = async (
      priceId: string,
    ): Promise<void> => {
      try {
        await paymentAPI.createAndRedirectToPayment({
          priceId,
          successUrl: generateSuccessUrl(),
          cancelUrl: generateCancelUrl(),
        })
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Payment failed'
        error.value = errorMessage
        throw new Error(errorMessage)
      }
    }

    const showRechargeModal = () => {
      visible.value = true
    }

    const hideRechargeModal = () => {
      visible.value = false
    }

    const toggleRechargeModal = () => {
      visible.value = !visible.value
    }

    const reset = () => {
      priceList.value = []
      error.value = null
      paymentLoading.value = false
    }

    // 生成成功回调URL
    const generateSuccessUrl = (): string => {
      if (typeof window !== 'undefined') {
        const baseUrl = window.location.origin

        if (isPlayshot()) {
          return `${baseUrl}/recharge-success`
        } else {
          return `${baseUrl}/payment/stripe-callback`
        }
      }
      return ''
    }

    // 生成取消回调URL
    const generateCancelUrl = (): string => {
      if (typeof window !== 'undefined') {
        return window.location.href
      }
      return ''
    }

    return {
      // State
      visible,
      priceList,
      loading,
      error,
      paymentLoading,

      // Computed
      projectConfig: readonly(ref(projectConfig)),

      // Actions
      fetchPriceList,
      createAndRedirectToPayment,
      showRechargeModal,
      hideRechargeModal,
      toggleRechargeModal,
      reset,
    }
  },
  {
    persist: false,
  },
)
