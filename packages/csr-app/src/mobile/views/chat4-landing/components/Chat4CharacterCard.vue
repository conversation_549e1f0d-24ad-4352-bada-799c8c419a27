<template>
  <div 
    class="chat4-character-card"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- Card Background Glow -->
    <div class="card-glow" :class="{ active: isHovered }"></div>
    
    <!-- Card Content -->
    <div class="card-content">
      <!-- Character Image -->
      <div class="character-image-container">
        <img 
          :src="character.avatar_url || story.preview_url" 
          :alt="character.name"
          class="character-image"
          @load="handleImageLoad"
          @error="handleImageError"
        />
        <div class="image-overlay"></div>
        
        <!-- Play Button Overlay -->
        <div class="play-button-overlay">
          <div class="play-button">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M8 5v14l11-7z" fill="currentColor"/>
            </svg>
          </div>
        </div>
      </div>
      
      <!-- Character Info -->
      <div class="character-info">
        <h3 class="character-name">{{ character.name }}</h3>
        <p class="story-title">{{ story.title }}</p>
        <p class="story-description" v-if="story.description">
          {{ truncatedDescription }}
        </p>
        
        <!-- Stats -->
        <div class="character-stats">
          <div class="stat-item" v-if="story.hot">
            <span class="stat-icon">🔥</span>
            <span class="stat-value">{{ formatNumber(story.hot) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-icon">⭐</span>
            <span class="stat-value">Interactive</span>
          </div>
        </div>
      </div>
      
      <!-- Action Button -->
      <div class="card-action">
        <button class="play-now-button">
          <span class="button-text">Play Now</span>
          <span class="button-icon">→</span>
        </button>
      </div>
    </div>
    
    <!-- Card Border Effect -->
    <div class="card-border"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  story: any
  character: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  click: [story: any, character: any]
}>()

const isHovered = ref(false)
const imageLoaded = ref(false)

const truncatedDescription = computed(() => {
  if (!props.story.description) return ''
  return props.story.description.length > 100 
    ? props.story.description.substring(0, 100) + '...'
    : props.story.description
})

const handleClick = () => {
  emit('click', props.story, props.character)
}

const handleMouseEnter = () => {
  isHovered.value = true
}

const handleMouseLeave = () => {
  isHovered.value = false
}

const handleImageLoad = () => {
  imageLoaded.value = true
}

const handleImageError = () => {
  console.warn(`Failed to load image for character: ${props.character.name}`)
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}
</script>

<style lang="less" scoped>
.chat4-character-card {
  position: relative;
  background: var(--chat4-bg-glass);
  backdrop-filter: blur(20px);
  border-radius: var(--chat4-radius-large);
  border: 1px solid var(--chat4-border-color);
  overflow: hidden;
  cursor: pointer;
  transition: all var(--chat4-transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
  height: 420px;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: var(--chat4-shadow-heavy);
    border-color: var(--chat4-primary-color);
    
    .character-image {
      transform: scale(1.05);
    }
    
    .play-button-overlay {
      opacity: 1;
    }
    
    .play-now-button {
      background: var(--chat4-gradient-primary);
      transform: translateY(-2px);
    }
  }
  
  &:active {
    transform: translateY(-4px);
  }
}

.card-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--chat4-gradient-primary);
  border-radius: var(--chat4-radius-large);
  opacity: 0;
  transition: opacity var(--chat4-transition-normal);
  z-index: -1;
  
  &.active {
    opacity: 0.3;
  }
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.character-image-container {
  position: relative;
  height: 240px;
  overflow: hidden;
  border-radius: var(--chat4-radius-medium) var(--chat4-radius-medium) 0 0;
  
  .character-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--chat4-transition-slow);
  }
  
  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      180deg,
      transparent 0%,
      rgba(0, 0, 0, 0.3) 70%,
      rgba(0, 0, 0, 0.7) 100%
    );
  }
}

.play-button-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity var(--chat4-transition-normal);
  
  .play-button {
    width: 60px;
    height: 60px;
    background: var(--chat4-gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: var(--chat4-shadow-medium);
    animation: pulse 2s infinite;
  }
}

.character-info {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .character-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--chat4-text-primary);
    margin-bottom: 8px;
    line-height: 1.2;
  }
  
  .story-title {
    font-size: 0.9rem;
    color: var(--chat4-secondary-color);
    font-weight: 600;
    margin-bottom: 8px;
  }
  
  .story-description {
    font-size: 0.85rem;
    color: var(--chat4-text-secondary);
    line-height: 1.4;
    margin-bottom: 16px;
    flex: 1;
  }
}

.character-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  
  .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    color: var(--chat4-text-secondary);
    
    .stat-icon {
      font-size: 0.9rem;
    }
    
    .stat-value {
      font-weight: 500;
    }
  }
}

.card-action {
  padding: 0 20px 20px;
  
  .play-now-button {
    width: 100%;
    padding: 12px 20px;
    background: var(--chat4-bg-secondary);
    border: 1px solid var(--chat4-border-color);
    border-radius: var(--chat4-radius-medium);
    color: var(--chat4-text-primary);
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--chat4-transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    
    .button-icon {
      transition: transform var(--chat4-transition-fast);
    }
    
    &:hover .button-icon {
      transform: translateX(4px);
    }
  }
}

.card-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--chat4-radius-large);
  border: 1px solid transparent;
  background: linear-gradient(135deg, var(--chat4-primary-color), var(--chat4-secondary-color)) border-box;
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity var(--chat4-transition-normal);
  pointer-events: none;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .chat4-character-card {
    height: 380px;
    
    &:hover {
      transform: translateY(-4px);
    }
  }
  
  .character-image-container {
    height: 200px;
  }
  
  .character-info {
    padding: 16px;
    
    .character-name {
      font-size: 1.1rem;
    }
  }
  
  .card-action {
    padding: 0 16px 16px;
  }
}
</style>
