<template>
  <div class="chat4-landing-page">
    <!-- 粒子背景 -->
    <Chat4ParticleBackground />

    <!-- 主要内容 -->
    <div class="landing-content" :class="{ 'chat-active': selectedCharacter }">
      <!-- 落地页内容 -->
      <div v-if="!selectedCharacter" class="landing-main">
        <!-- 英雄区域 -->
        <div class="hero-section">
          <div class="hero-content">
            <h1 class="hero-title">
              <span class="title-gradient">Chat4</span>
              <span class="title-subtitle">Interactive Entertainment</span>
            </h1>
            <p class="hero-description">
              Experience next-generation interactive entertainment with live
              streaming, real-time chat, and immersive AI-powered experiences.
            </p>
          </div>
        </div>

        <!-- 角色网格 -->
        <div class="characters-section">
          <h2 class="section-title">Choose Your Adventure</h2>
          <div v-if="isLoading" class="loading-grid">
            <div v-for="i in 6" :key="i" class="character-skeleton" />
          </div>
          <div v-else class="characters-grid">
            <Chat4CharacterCard
              v-for="story in filteredStories"
              :key="`${story.id}-${story.actors?.[0]?.id}`"
              :story="story"
              :character="story.actors?.[0]"
              @click="handleCharacterClick"
            />
          </div>
        </div>
      </div>

      <!-- 聊天界面 -->
      <Transition name="chat-slide" appear>
        <div v-if="selectedCharacter" class="chat-container">
          <!-- 返回按钮 -->
          <button class="back-button chat4-button" @click="handleBackToLanding">
            <Icon name="mdi:arrow-left" />
            <span>Back to Characters</span>
          </button>

          <!-- 角色信息条 -->
          <div class="character-info-bar">
            <div class="character-avatar">
              <NuxtImg
                :src="
                  selectedCharacter.characterAvatar ||
                  '/images/default-avatar.jpg'
                "
                :alt="selectedCharacter.characterName"
                class="avatar-image"
              />
            </div>
            <div class="character-details">
              <h3 class="character-name">{{
                selectedCharacter.characterName
              }}</h3>
              <p class="story-title">{{ selectedCharacter.storyTitle }}</p>
            </div>
            <div class="live-indicator">
              <div class="live-dot" />
              <span>LIVE</span>
            </div>
          </div>

          <!-- 聊天加载器 -->
          <div class="chat-loader-container">
            <RemoteChatLoader
              chat-type="chat4"
              :character-id="selectedCharacter.actorId"
              :story-id="selectedCharacter.storyId"
            />
          </div>
        </div>
      </Transition>
    </div>

    <!-- SEO 内容 -->
    <div class="seo-content">
      <h1 class="seo-hidden"
        >Chat4 - Premium Interactive Entertainment Platform</h1
      >
      <h2 class="seo-hidden"
        >Experience Live AI Chat with Beautiful Characters</h2
      >
      <h3 class="seo-hidden"
        >Free Trial Available - Start Your Adventure Today</h3
      >
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  layout: 'mobile',
})

// 导入组件
import Chat4ParticleBackground from '~/components/Chat4ParticleBackground.vue'
import Chat4CharacterCard from '~/components/Chat4CharacterCard.vue'
import RemoteChatLoader from '~/components/RemoteChatLoader.vue'

// 响应式数据
const isLoading = ref(true)
const stories = ref([])
const selectedCharacter = ref(null)

// 计算属性
const filteredStories = computed(() => {
  return stories.value.filter((story) => {
    return story.actors && story.actors.length > 0
  })
})

// 方法
const handleCharacterClick = (story: any, character: any) => {
  // 设置选中的角色
  selectedCharacter.value = {
    storyId: story.id,
    actorId: character.id,
    storyTitle: story.title,
    characterName: character.name,
    characterAvatar: character.avatar_url,
  }

  // 报告分析事件
  if (import.meta.client) {
    // 这里可以添加分析事件报告
    console.log('Character selected:', {
      storyId: story.id,
      actorId: character.id,
      characterName: character.name,
      buttonType: 'chat4LandingCard',
      chatMode: 'chat4',
    })

    // 可以在这里添加 Google Analytics 或其他分析工具的事件追踪
    // gtag('event', 'character_selected', {
    //   story_id: story.id,
    //   character_id: character.id,
    //   character_name: character.name
    // })
  }
}

const handleBackToLanding = () => {
  selectedCharacter.value = null
}

const initializePage = async () => {
  try {
    isLoading.value = true

    // 获取故事数据
    const { data: storyData } = await $fetch('/api/story/list', {
      method: 'GET',
      query: {
        page: 1,
        pageSize: 20,
      },
    })

    if (storyData?.list) {
      stories.value = storyData.list
    }
  } catch (error) {
    console.error('Failed to load stories:', error)
  } finally {
    isLoading.value = false
  }
}

// SEO 设置
usePageSeo({
  title: 'Chat4 - Interactive Entertainment Platform',
  description:
    'Experience next-generation interactive entertainment with live streaming, real-time chat, and immersive AI-powered experiences. Free trial available.',
  keywords:
    'chat4, interactive entertainment, AI chat, live streaming, virtual characters, otome games',
  ogImage: '/images/chat4-og.jpg',
})

// 页面初始化
onMounted(() => {
  initializePage()
})
</script>

<style lang="less" scoped>
@import '~/assets/css/chat4.less';

.chat4-landing-page {
  min-height: 100vh;
  background: var(--chat4-bg-primary);
  color: var(--chat4-text-primary);
  position: relative;
  overflow-x: hidden;
}

.landing-content {
  position: relative;
  z-index: 2;
  transition: all var(--chat4-transition-slow) ease;

  &.chat-active {
    .landing-main {
      opacity: 0;
      pointer-events: none;
    }
  }
}

.landing-main {
  transition: all var(--chat4-transition-slow) ease;
}

.hero-section {
  padding: var(--chat4-spacing-xl) var(--chat4-spacing-lg);
  text-align: center;
  min-height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.hero-title {
  font-size: clamp(2rem, 8vw, 4rem);
  font-weight: 800;
  margin-bottom: var(--chat4-spacing-lg);
  line-height: 1.2;

  .title-gradient {
    background: var(--chat4-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
  }

  .title-subtitle {
    display: block;
    font-size: 0.5em;
    font-weight: 400;
    color: var(--chat4-text-secondary);
    margin-top: var(--chat4-spacing-sm);
  }
}

.hero-description {
  font-size: var(--chat4-font-lg);
  color: var(--chat4-text-secondary);
  line-height: 1.6;
  margin-bottom: var(--chat4-spacing-xl);
}

.characters-section {
  padding: var(--chat4-spacing-xl) var(--chat4-spacing-lg);
}

.section-title {
  font-size: var(--chat4-font-3xl);
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--chat4-spacing-xl);
  background: var(--chat4-gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--chat4-spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.character-skeleton {
  height: 200px;
  background: var(--chat4-bg-glass);
  border-radius: var(--chat4-radius-large);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--chat4-spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.chat-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: var(--chat4-bg-primary);
  z-index: 10;
  display: flex;
  flex-direction: column;
}

.character-info-bar {
  position: relative;
  z-index: 20;
  display: flex;
  align-items: center;
  padding: var(--chat4-spacing-md) var(--chat4-spacing-lg);
  background: var(--chat4-bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--chat4-border-color);
  gap: var(--chat4-spacing-md);
}

.character-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--chat4-radius-round);
  overflow: hidden;
  border: 2px solid var(--chat4-primary-color);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-details {
  flex: 1;
}

.character-details .character-name {
  font-size: var(--chat4-font-md);
  font-weight: 700;
  color: var(--chat4-text-primary);
  margin: 0 0 var(--chat4-spacing-xs) 0;
}

.character-details .story-title {
  font-size: var(--chat4-font-sm);
  color: var(--chat4-text-secondary);
  margin: 0;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: var(--chat4-spacing-xs);
  padding: var(--chat4-spacing-xs) var(--chat4-spacing-sm);
  background: rgba(76, 175, 80, 0.2);
  border-radius: var(--chat4-radius-small);
  font-size: var(--chat4-font-xs);
  font-weight: 600;
  color: var(--chat4-success-color);
}

.live-dot {
  width: 6px;
  height: 6px;
  background: var(--chat4-success-color);
  border-radius: var(--chat4-radius-round);
  animation: pulse-dot 2s infinite;
}

.chat-loader-container {
  flex: 1;
  position: relative;
}

.back-button {
  position: absolute;
  top: var(--chat4-spacing-lg);
  left: var(--chat4-spacing-lg);
  z-index: 20;
  display: flex;
  align-items: center;
  gap: var(--chat4-spacing-sm);
  padding: var(--chat4-spacing-sm) var(--chat4-spacing-md);
  font-size: var(--chat4-font-sm);

  svg {
    width: 16px;
    height: 16px;
  }
}

.seo-content {
  position: absolute;
  left: -9999px;
  top: -9999px;
}

.seo-hidden {
  position: absolute;
  left: -9999px;
  top: -9999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

// 过渡动画
.chat-slide-enter-active,
.chat-slide-leave-active {
  transition: all var(--chat4-transition-slow) ease;
}

.chat-slide-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.chat-slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .hero-section {
    padding: var(--chat4-spacing-lg) var(--chat4-spacing-md);
    min-height: 40vh;
  }

  .characters-section {
    padding: var(--chat4-spacing-lg) var(--chat4-spacing-md);
  }

  .characters-grid {
    grid-template-columns: 1fr;
    gap: var(--chat4-spacing-md);
  }

  .back-button {
    top: var(--chat4-spacing-md);
    left: var(--chat4-spacing-md);
    padding: var(--chat4-spacing-xs) var(--chat4-spacing-sm);
    font-size: var(--chat4-font-xs);
  }

  .character-info-bar {
    padding: var(--chat4-spacing-sm) var(--chat4-spacing-md);
  }

  .character-avatar {
    width: 32px;
    height: 32px;
  }
}

@media (min-width: 1200px) {
  .characters-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
