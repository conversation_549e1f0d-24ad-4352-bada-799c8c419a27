<template>
  <div class="chat4-landing-page">
    <!-- 粒子背景 -->
    <Chat4ParticleBackground />

    <!-- 主要内容 -->
    <div class="landing-content" :class="{ 'chat-active': selectedCharacter }">
      <!-- 落地页内容 -->
      <div v-if="!selectedCharacter" class="landing-main">
        <!-- 英雄区域 -->
        <div class="hero-section">
          <div class="hero-content">
            <h1 class="hero-title">
              <span class="title-gradient">Chat4</span>
              <span class="title-subtitle"
                >Discover Your Perfect Connection</span
              >
            </h1>
            <p class="hero-description">
              Meet charming companions and create meaningful conversations in a
              beautiful, interactive world. Each character has their own unique
              story waiting to be discovered.
            </p>
          </div>
        </div>

        <!-- 角色网格 -->
        <div class="characters-section">
          <h2 class="section-title">Choose Your Companion</h2>
          <p class="section-subtitle"
            >Each character has their own unique personality and story to
            share</p
          >
          <div v-if="isLoading" class="loading-grid">
            <div v-for="i in 6" :key="i" class="character-skeleton" />
          </div>
          <div v-else class="characters-grid">
            <Chat4CharacterCard
              v-for="character in availableCharacters"
              :key="character.characterKey"
              :character="character"
              @click="handleCharacterClick"
            />
          </div>
        </div>
      </div>

      <!-- 聊天界面 -->
      <Transition name="chat-slide" appear>
        <div v-if="selectedCharacter" class="chat-container">
          <!-- 返回按钮 -->
          <button class="back-button chat4-button" @click="handleBackToLanding">
            <Icon name="mdi:arrow-left" />
            <span>Back to Characters</span>
          </button>

          <!-- 角色信息条 -->
          <div class="character-info-bar">
            <div class="character-avatar">
              <NuxtImg
                :src="
                  selectedCharacter.characterAvatar ||
                  '/images/default-avatar.jpg'
                "
                :alt="selectedCharacter.characterName"
                class="avatar-image"
              />
            </div>
            <div class="character-details">
              <h3 class="character-name">{{
                selectedCharacter.characterName
              }}</h3>
              <p class="story-title">{{ selectedCharacter.storyTitle }}</p>
            </div>
            <div class="live-indicator">
              <div class="live-dot" />
              <span>LIVE</span>
            </div>
          </div>

          <!-- 聊天加载器 -->
          <div class="chat-loader-container">
            <RemoteChatLoader
              chat-type="chat4"
              :character-id="selectedCharacter.actorId"
              :story-id="selectedCharacter.storyId"
            />
          </div>
        </div>
      </Transition>
    </div>

    <!-- SEO 内容 -->
    <div class="seo-content">
      <h1 class="seo-hidden">Chat4 - Premium Romance Simulation for Women</h1>
      <h2 class="seo-hidden"
        >Meet Handsome Virtual Boyfriends and Create Meaningful Connections</h2
      >
      <h3 class="seo-hidden"
        >Free Otome Game Experience - Start Your Love Story Today</h3
      >
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  layout: 'mobile',
})

// 导入组件和配置
import Chat4ParticleBackground from '~/components/Chat4ParticleBackground.vue'
import Chat4CharacterCard from '~/components/Chat4CharacterCard.vue'
import RemoteChatLoader from '~/components/RemoteChatLoader.vue'
import {
  chat4LandingConfigs,
  getAvailableCharacterKeys,
} from '~/config/chat4-landing'

// 响应式数据
const isLoading = ref(true)
const selectedCharacter = ref(null)

// 计算属性 - 使用配置系统
const availableCharacters = computed(() => {
  const characterKeys = getAvailableCharacterKeys()
  return characterKeys.map((key) => {
    const config = chat4LandingConfigs[key]
    return {
      characterKey: key,
      storyId: config.storyId,
      characterName: config.characterName,
      storyQuote: config.storyQuote,
      themeColor: config.themeColor,
      characterAvatar:
        config.characterAvatar || `/images/characters/${key}.jpg`,
      backgroundImage:
        config.backgroundImage || `/images/backgrounds/${key}.jpg`,
    }
  })
})

// 方法
const handleCharacterClick = (character: any) => {
  // 设置选中的角色
  selectedCharacter.value = {
    storyId: character.storyId,
    actorId: character.characterKey, // 使用 characterKey 作为 actorId
    storyTitle: `Chat with ${character.characterName}`,
    characterName: character.characterName,
    characterAvatar: character.characterAvatar,
    characterKey: character.characterKey,
    themeColor: character.themeColor,
  }

  // 报告分析事件
  if (import.meta.client) {
    // 这里可以添加分析事件报告
    console.log('Character selected:', {
      storyId: character.storyId,
      characterKey: character.characterKey,
      characterName: character.characterName,
      buttonType: 'chat4LandingCard',
      chatMode: 'chat4',
    })

    // 可以在这里添加 Google Analytics 或其他分析工具的事件追踪
    // gtag('event', 'character_selected', {
    //   story_id: character.storyId,
    //   character_key: character.characterKey,
    //   character_name: character.characterName
    // })
  }
}

const handleBackToLanding = () => {
  selectedCharacter.value = null
}

const initializePage = async () => {
  try {
    isLoading.value = true

    // 模拟加载时间以显示加载动画
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 角色数据来自配置文件，无需 API 调用
    console.log(
      'Chat4 landing page initialized with',
      availableCharacters.value.length,
      'characters',
    )
  } catch (error) {
    console.error('Failed to initialize Chat4 landing page:', error)
  } finally {
    isLoading.value = false
  }
}

// SEO 设置
usePageSeo({
  title: 'Chat4 - Discover Your Perfect Connection',
  description:
    'Meet charming companions and create meaningful conversations in a beautiful, interactive world. Chat with handsome characters designed for female users. Free to start!',
  keywords:
    'chat4, otome games, interactive romance, AI boyfriend, virtual dating, female gamers, romance simulation, character chat',
  ogImage: '/images/chat4-og.jpg',
})

// 页面初始化
onMounted(() => {
  initializePage()
})
</script>

<style lang="less" scoped>
@import '~/assets/css/chat4.less';

.chat4-landing-page {
  min-height: 100vh;
  background: var(--chat4-bg-primary);
  color: var(--chat4-text-primary);
  position: relative;
  overflow-x: hidden;

  // 添加柔和的背景纹理
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 20% 80%,
        rgba(156, 136, 255, 0.03) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 20%,
        rgba(255, 158, 181, 0.03) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 40% 40%,
        rgba(168, 230, 207, 0.02) 0%,
        transparent 50%
      );
    pointer-events: none;
    z-index: 1;
  }
}

.landing-content {
  position: relative;
  z-index: 2;
  transition: all var(--chat4-transition-slow) ease;

  &.chat-active {
    .landing-main {
      opacity: 0;
      pointer-events: none;
    }
  }
}

.landing-main {
  transition: all var(--chat4-transition-slow) ease;
}

.hero-section {
  padding: var(--chat4-spacing-xl) var(--chat4-spacing-lg);
  text-align: center;
  min-height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.hero-title {
  font-size: clamp(2rem, 8vw, 4rem);
  font-weight: 800;
  margin-bottom: var(--chat4-spacing-lg);
  line-height: 1.2;

  .title-gradient {
    background: var(--chat4-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
  }

  .title-subtitle {
    display: block;
    font-size: 0.5em;
    font-weight: 400;
    color: var(--chat4-text-secondary);
    margin-top: var(--chat4-spacing-sm);
  }
}

.hero-description {
  font-size: var(--chat4-font-lg);
  color: var(--chat4-text-secondary);
  line-height: 1.6;
  margin-bottom: var(--chat4-spacing-xl);
}

.characters-section {
  padding: var(--chat4-spacing-xl) var(--chat4-spacing-lg);
}

.section-title {
  font-size: var(--chat4-font-3xl);
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--chat4-spacing-md);
  background: var(--chat4-gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: var(--chat4-font-lg);
  color: var(--chat4-text-secondary);
  text-align: center;
  margin-bottom: var(--chat4-spacing-xl);
  line-height: 1.6;
}

.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--chat4-spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.character-skeleton {
  height: 200px;
  background: var(--chat4-bg-glass);
  border-radius: var(--chat4-radius-large);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--chat4-spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.chat-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: var(--chat4-bg-primary);
  z-index: 10;
  display: flex;
  flex-direction: column;
}

.character-info-bar {
  position: relative;
  z-index: 20;
  display: flex;
  align-items: center;
  padding: var(--chat4-spacing-md) var(--chat4-spacing-lg);
  background: var(--chat4-bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--chat4-border-color);
  gap: var(--chat4-spacing-md);
}

.character-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--chat4-radius-round);
  overflow: hidden;
  border: 2px solid var(--chat4-primary-color);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-details {
  flex: 1;
}

.character-details .character-name {
  font-size: var(--chat4-font-md);
  font-weight: 700;
  color: var(--chat4-text-primary);
  margin: 0 0 var(--chat4-spacing-xs) 0;
}

.character-details .story-title {
  font-size: var(--chat4-font-sm);
  color: var(--chat4-text-secondary);
  margin: 0;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: var(--chat4-spacing-xs);
  padding: var(--chat4-spacing-xs) var(--chat4-spacing-sm);
  background: rgba(76, 175, 80, 0.2);
  border-radius: var(--chat4-radius-small);
  font-size: var(--chat4-font-xs);
  font-weight: 600;
  color: var(--chat4-success-color);
}

.live-dot {
  width: 6px;
  height: 6px;
  background: var(--chat4-success-color);
  border-radius: var(--chat4-radius-round);
  animation: pulse-dot 2s infinite;
}

.chat-loader-container {
  flex: 1;
  position: relative;
}

.back-button {
  position: absolute;
  top: var(--chat4-spacing-lg);
  left: var(--chat4-spacing-lg);
  z-index: 20;
  display: flex;
  align-items: center;
  gap: var(--chat4-spacing-sm);
  padding: var(--chat4-spacing-sm) var(--chat4-spacing-md);
  font-size: var(--chat4-font-sm);

  svg {
    width: 16px;
    height: 16px;
  }
}

.seo-content {
  position: absolute;
  left: -9999px;
  top: -9999px;
}

.seo-hidden {
  position: absolute;
  left: -9999px;
  top: -9999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

// 过渡动画
.chat-slide-enter-active,
.chat-slide-leave-active {
  transition: all var(--chat4-transition-slow) ease;
}

.chat-slide-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.chat-slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .hero-section {
    padding: var(--chat4-spacing-lg) var(--chat4-spacing-md);
    min-height: 40vh;
  }

  .characters-section {
    padding: var(--chat4-spacing-lg) var(--chat4-spacing-md);
  }

  .characters-grid {
    grid-template-columns: 1fr;
    gap: var(--chat4-spacing-md);
  }

  .back-button {
    top: var(--chat4-spacing-md);
    left: var(--chat4-spacing-md);
    padding: var(--chat4-spacing-xs) var(--chat4-spacing-sm);
    font-size: var(--chat4-font-xs);
  }

  .character-info-bar {
    padding: var(--chat4-spacing-sm) var(--chat4-spacing-md);
  }

  .character-avatar {
    width: 32px;
    height: 32px;
  }
}

@media (min-width: 1200px) {
  .characters-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
