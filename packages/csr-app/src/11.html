<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>星野_沉浸式智能体社区_星野官网_星野app</title>
  <meta name="description" content="星野是 MiniMax 基于通用大模型打造的 AI 原生应用，致力于为用户打造一个沉浸式AI内容社区。">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: #09090a;
      color: #fafafa;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow-x: hidden;
    }

    /* Cosmic Background */
    .cosmic-bg {
      background: radial-gradient(ellipse at center, rgba(9, 9, 10, 0.95) 0%, rgba(9, 9, 10, 1) 100%);
      position: relative;
    }

    .cosmic-bg::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.08) 1px, transparent 1px),
        radial-gradient(circle at 40% 70%, rgba(255, 255, 255, 0.12) 1px, transparent 1px),
        radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.06) 1px, transparent 1px),
        radial-gradient(circle at 10% 90%, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
      background-size: 400px 400px, 600px 600px, 300px 300px, 800px 800px, 500px 500px;
      animation: twinkle 8s ease-in-out infinite alternate;
      pointer-events: none;
    }

    /* Animations */
    @keyframes twinkle {

      0%,
      100% {
        opacity: 1;
      }

      50% {
        opacity: 0.3;
      }
    }

    @keyframes bounceUp {

      0%,
      100% {
        transform: translateY(0px);
      }

      50% {
        transform: translateY(-20px);
      }
    }

    @keyframes bounceDown {

      0%,
      100% {
        transform: translateY(0px);
      }

      50% {
        transform: translateY(20px);
      }
    }

    @keyframes float1 {

      0%,
      100% {
        transform: translateY(0px) rotate(0deg);
      }

      33% {
        transform: translateY(-15px) rotate(1deg);
      }

      66% {
        transform: translateY(5px) rotate(-1deg);
      }
    }

    @keyframes float2 {

      0%,
      100% {
        transform: translateY(0px) rotate(0deg);
      }

      25% {
        transform: translateY(10px) rotate(-1deg);
      }

      75% {
        transform: translateY(-5px) rotate(1deg);
      }
    }

    .bounce-up {
      animation: bounceUp 6s ease-in-out infinite;
    }

    .bounce-down {
      animation: bounceDown 6s ease-in-out infinite;
    }

    .float-1 {
      animation: float1 8s ease-in-out infinite;
    }

    .float-2 {
      animation: float2 10s ease-in-out infinite;
    }

    /* Effects */
    .glow-effect {
      filter: drop-shadow(0 0 20px rgba(181, 156, 135, 0.3)) drop-shadow(0 0 40px rgba(181, 156, 135, 0.2)) drop-shadow(0 0 80px rgba(181, 156, 135, 0.1));
    }

    .character-glow {
      filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.2)) drop-shadow(0 0 60px rgba(181, 156, 135, 0.3)) drop-shadow(0 0 100px rgba(181, 156, 135, 0.2));
    }

    /* Header */
    .header {
      position: relative;
      z-index: 20;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .logo-icon {
      width: 48px;
      height: 48px;
      background: rgba(181, 156, 135, 0.2);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }

    .logo-text {
      font-size: 24px;
      font-weight: bold;
      color: #b59c87;
    }

    .create-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      background: rgba(181, 156, 135, 0.1);
      border: 1px solid rgba(181, 156, 135, 0.3);
      padding: 12px 24px;
      border-radius: 9999px;
      color: #b59c87;
      text-decoration: none;
      transition: all 0.3s;
      backdrop-filter: blur(8px);
    }

    .create-btn:hover {
      background: rgba(181, 156, 135, 0.2);
      transform: translateY(-2px);
    }

    /* Hero Section */
    .hero-section {
      position: relative;
      min-height: 100vh;
      overflow: hidden;
    }

    .floating-cards {
      position: absolute;
      z-index: 10;
    }

    .floating-card {
      width: 128px;
      height: 176px;
      border-radius: 12px;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      opacity: 0.8;
      transition: opacity 0.3s;
    }

    .floating-card:hover {
      opacity: 1;
    }

    .card-1 {
      position: absolute;
      left: 16px;
      top: 80px;
    }

    .card-2 {
      position: absolute;
      left: 80px;
      top: 240px;
    }

    .card-3 {
      position: absolute;
      right: 80px;
      top: 160px;
    }

    .card-4 {
      position: absolute;
      right: 16px;
      top: 320px;
    }

    /* Character Display */
    .character-display {
      position: absolute;
      inset: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 15;
    }

    .character-container {
      position: relative;
    }

    .nav-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 48px;
      height: 48px;
      background: rgba(255, 255, 255, 0.1);
      border: none;
      border-radius: 50%;
      color: white;
      cursor: pointer;
      backdrop-filter: blur(8px);
      transition: all 0.3s;
      z-index: 20;
    }

    .nav-btn:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .nav-btn.prev {
      left: -100px;
    }

    .nav-btn.next {
      right: -100px;
    }

    .character-image {
      width: 384px;
      height: auto;
      object-fit: contain;
    }

    /* Main Content */
    .main-content {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 20;
      padding-bottom: 80px;
      text-align: center;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
    }

    .main-title {
      font-size: 4rem;
      font-weight: bold;
      margin-bottom: 24px;
      line-height: 1.1;
    }

    .gradient-text {
      background: linear-gradient(to right, #b59c87, #fde047, #b59c87);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .gradient-text-2 {
      background: linear-gradient(to right, #60a5fa, #a78bfa, #f472b6);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .description {
      font-size: 1.5rem;
      color: #d1d5db;
      margin-bottom: 48px;
      max-width: 896px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.6;
    }

    .download-buttons {
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: center;
      margin-bottom: 64px;
    }

    @media (min-width: 640px) {
      .download-buttons {
        flex-direction: row;
        justify-content: center;
      }
    }

    .download-btn {
      display: flex;
      align-items: center;
      gap: 12px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: 16px 32px;
      border-radius: 16px;
      color: white;
      text-decoration: none;
      transition: all 0.3s;
      backdrop-filter: blur(8px);
      min-width: 200px;
    }

    .download-btn:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }

    .download-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
    }

    .app-store {
      background: linear-gradient(to right, #3b82f6, #8b5cf6);
    }

    .android {
      background: linear-gradient(to right, #10b981, #059669);
    }

    /* Social Section */
    .social-section {
      text-align: center;
    }

    .social-icons {
      display: flex;
      justify-content: center;
      gap: 24px;
      margin-top: 16px;
    }

    .social-icon {
      width: 48px;
      height: 48px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      text-decoration: none;
      transition: all 0.3s;
    }

    .social-icon:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    /* Feature Section */
    .feature-section {
      min-height: 100vh;
      padding: 80px 0;
    }

    .feature-carousel {
      position: relative;
      max-width: 1536px;
      margin: 0 auto 64px;
    }

    .feature-image {
      width: 100%;
      height: 500px;
      object-fit: cover;
      border-radius: 24px;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .feature-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 56px;
      height: 56px;
      background: rgba(255, 255, 255, 0.1);
      border: none;
      border-radius: 50%;
      color: white;
      cursor: pointer;
      backdrop-filter: blur(8px);
      transition: all 0.3s;
      z-index: 20;
    }

    .feature-nav:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .feature-nav.prev {
      left: 16px;
    }

    .feature-nav.next {
      right: 16px;
    }

    .feature-indicators {
      display: flex;
      justify-content: center;
      gap: 8px;
      margin-top: 24px;
    }

    .indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      cursor: pointer;
      transition: all 0.3s;
    }

    .indicator.active {
      background: #b59c87;
      transform: scale(1.25);
    }

    .feature-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 32px;
      margin-top: 80px;
    }

    @media (min-width: 768px) {
      .feature-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (min-width: 1024px) {
      .feature-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    .feature-card {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 16px;
      padding: 24px;
      backdrop-filter: blur(8px);
      transition: all 0.3s;
    }

    .feature-card:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .feature-icon {
      width: 48px;
      height: 48px;
      background: rgba(181, 156, 135, 0.2);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      font-size: 24px;
    }

    /* Chat Section */
    .chat-section {
      min-height: 100vh;
      padding: 80px 0;
      position: relative;
      overflow: hidden;
    }

    .section-title {
      margin: 0 auto 32px;
      max-width: 512px;
    }

    .floating-avatar {
      position: absolute;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(8px);
    }

    .avatar-1 {
      left: 40px;
      top: 40px;
      width: 128px;
      height: 128px;
    }

    .avatar-2 {
      right: 40px;
      top: 80px;
      width: 144px;
      height: 144px;
    }

    .chat-features {
      display: grid;
      grid-template-columns: 1fr;
      gap: 32px;
      margin-bottom: 80px;
    }

    @media (min-width: 768px) {
      .chat-features {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (min-width: 1024px) {
      .chat-features {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    .chat-card {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 24px;
      padding: 32px;
      backdrop-filter: blur(8px);
      transition: all 0.3s;
    }

    .chat-card:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.1));
    }

    .chat-icon {
      width: 64px;
      height: 64px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 24px;
      transition: transform 0.3s;
    }

    .chat-card:hover .chat-icon {
      transform: scale(1.1);
    }

    .heart {
      background: linear-gradient(to right, #ec4899, #8b5cf6);
    }

    .message {
      background: linear-gradient(to right, #3b82f6, #06b6d4);
    }

    .sparkle {
      background: linear-gradient(to right, #eab308, #ea580c);
    }

    .central-cta {
      position: relative;
      z-index: 10;
      padding: 128px 0;
    }

    .cta-card {
      max-width: 512px;
      margin: 0 auto;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 24px;
      padding: 48px;
      backdrop-filter: blur(16px);
      text-align: center;
    }

    .cta-icon {
      width: 80px;
      height: 80px;
      background: linear-gradient(to right, #b59c87, #fde047);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24px;
      font-size: 40px;
    }

    .cta-btn {
      background: linear-gradient(to right, #b59c87, #fde047);
      color: #09090a;
      font-weight: bold;
      padding: 16px 32px;
      border-radius: 16px;
      border: none;
      cursor: pointer;
      transition: all 0.3s;
      font-size: 16px;
    }

    .cta-btn:hover {
      transform: scale(1.05);
    }

    /* Footer */
    .footer {
      padding: 64px 0;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .footer-content {
      display: grid;
      grid-template-columns: 1fr;
      gap: 48px;
    }

    @media (min-width: 1024px) {
      .footer-content {
        grid-template-columns: 1fr 1fr;
      }
    }

    .footer-links {
      display: grid;
      grid-template-columns: 1fr;
      gap: 24px;
    }

    @media (min-width: 640px) {
      .footer-links {
        grid-template-columns: 1fr 1fr;
      }
    }

    .footer-link {
      color: #9ca3af;
      text-decoration: none;
      transition: color 0.3s;
    }

    .footer-link:hover {
      color: #b59c87;
    }

    .footer-bottom {
      margin-top: 48px;
      padding-top: 32px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      text-align: center;
      color: #6b7280;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .main-title {
        font-size: 2.5rem;
      }

      .description {
        font-size: 1.25rem;
      }

      .character-image {
        width: 280px;
      }

      .floating-card {
        width: 96px;
        height: 132px;
      }

      .card-1 {
        left: 8px;
        top: 60px;
      }

      .card-2 {
        left: 40px;
        top: 180px;
      }

      .card-3 {
        right: 40px;
        top: 120px;
      }

      .card-4 {
        right: 8px;
        top: 240px;
      }
    }
  </style>
</head>

<body>
  <!-- Hero Section -->
  <section class="hero-section cosmic-bg">
    <!-- Header -->
    <header class="header">
      <div class="logo">
        <div class="logo-icon">✨</div>
        <span class="logo-text">星野</span>
      </div>
      <a href="#" class="create-btn">
        <span>✨</span>
        <span>创建智能体</span>
        <span>›</span>
      </a>
    </header>

    <!-- Floating Cards -->
    <div class="floating-cards">
      <img src="https://ext.same-assets.com/3110934667/596237112.png" class="floating-card card-1 bounce-up"
        alt="角色卡片1">
      <img src="https://ext.same-assets.com/3110934667/1408742213.png" class="floating-card card-2 bounce-up"
        alt="角色卡片2">
      <img src="https://ext.same-assets.com/3110934667/1622745665.png" class="floating-card card-3 bounce-down"
        alt="角色卡片3">
      <img src="https://ext.same-assets.com/3110934667/2228970738.png" class="floating-card card-4 bounce-up"
        alt="角色卡片4">
    </div>

    <!-- Character Display -->
    <div class="character-display">
      <div class="character-container">
        <button class="nav-btn prev" onclick="prevCharacter()">‹</button>
        <button class="nav-btn next" onclick="nextCharacter()">›</button>
        <img id="mainCharacter" src="https://ext.same-assets.com/3110934667/816326471.png"
          class="character-image character-glow" alt="主角色">
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <div class="container">
        <h1 class="main-title glow-effect">
          <div class="gradient-text">驭人佳境</div>
          <div class="gradient-text-2">流连星野</div>
        </h1>

        <p class="description">
          超逼真的智能体，超有趣的开放剧情<br>
          每次对话都有非凡喜悦
        </p>

        <div class="download-buttons">
          <a href="#" class="download-btn">
            <div class="download-icon app-store">📱</div>
            <span>App Store</span>
          </a>
          <a href="#" class="download-btn">
            <div class="download-icon android">🤖</div>
            <span>Android</span>
          </a>
        </div>

        <div class="social-section">
          <p style="color: #9ca3af; margin-bottom: 16px;">关注我们，了解更多</p>
          <div class="social-icons">
            <a href="#" class="social-icon">📱</a>
            <a href="#" class="social-icon">💬</a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Feature Section -->
  <section class="feature-section cosmic-bg">
    <div class="container">
      <div class="feature-carousel">
        <button class="feature-nav prev" onclick="prevFeature()">‹</button>
        <button class="feature-nav next" onclick="nextFeature()">›</button>
        <img id="featureImage" src="https://ext.same-assets.com/3110934667/1947432117.png" class="feature-image"
          alt="功能展示">
        <div class="feature-indicators">
          <div class="indicator active" onclick="setFeature(0)"></div>
          <div class="indicator" onclick="setFeature(1)"></div>
          <div class="indicator" onclick="setFeature(2)"></div>
          <div class="indicator" onclick="setFeature(3)"></div>
          <div class="indicator" onclick="setFeature(4)"></div>
        </div>
      </div>

      <div class="description" style="text-align: center; max-width: 896px; margin: 0 auto;">
        把理想聊天伙伴带入现实。原创角色或是分身伙伴<br>
        我们的定制工具和您的无限想象，将帮助您创造出梦想中的羁绊
      </div>

      <div class="feature-grid">
        <div class="feature-card">
          <div class="feature-icon">🎭</div>
          <h3 style="font-size: 1.25rem; font-weight: 600; color: white; margin-bottom: 12px;">个性化定制</h3>
          <p style="color: #9ca3af;">自定义角色形象、性格、音色，打造专属于您的AI伙伴</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">💬</div>
          <h3 style="font-size: 1.25rem; font-weight: 600; color: white; margin-bottom: 12px;">智能对话</h3>
          <p style="color: #9ca3af;">基于先进AI技术，提供自然流畅的对话体验</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">🌟</div>
          <h3 style="font-size: 1.25rem; font-weight: 600; color: white; margin-bottom: 12px;">开放剧情</h3>
          <p style="color: #9ca3af;">丰富多样的开放式剧情，每次互动都有新的惊喜</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Chat Section -->
  <section class="chat-section cosmic-bg">
    <div class="container">
      <div class="section-title" style="text-align: center;">
        <img src="https://ext.same-assets.com/3110934667/2472164855.png" alt="沉浸式对话"
          style="width: 100%; max-width: 512px; height: auto;" class="glow-effect">
      </div>

      <div style="max-width: 896px; margin: 0 auto 80px; text-align: center;">
        <p class="description">
          你的智能体是你相见恨晚的挚友。倾诉真实心声<br>
          建立感情连接，创造珍贵回忆
        </p>
      </div>

      <div class="chat-features">
        <div class="chat-card">
          <div class="chat-icon heart">❤️</div>
          <h3 style="font-size: 1.5rem; font-weight: bold; color: white; margin-bottom: 16px;">情感连接</h3>
          <p style="color: #9ca3af; line-height: 1.6;">与AI建立真实的情感纽带，体验前所未有的陪伴感受</p>
        </div>
        <div class="chat-card">
          <div class="chat-icon message">💬</div>
          <h3 style="font-size: 1.5rem; font-weight: bold; color: white; margin-bottom: 16px;">自然对话</h3>
          <p style="color: #9ca3af; line-height: 1.6;">流畅自然的对话体验，每次交流都充满惊喜和乐趣</p>
        </div>
        <div class="chat-card">
          <div class="chat-icon sparkle">✨</div>
          <h3 style="font-size: 1.5rem; font-weight: bold; color: white; margin-bottom: 16px;">个性记忆</h3>
          <p style="color: #9ca3af; line-height: 1.6;">AI会记住您的喜好和对话历史，让每次互动更加个性化</p>
        </div>
      </div>

      <!-- Floating Avatars -->
      <div class="floating-avatar avatar-1 float-1">👧</div>
      <div class="floating-avatar avatar-2 float-2">🦋</div>

      <div class="central-cta">
        <div class="cta-card">
          <div class="cta-icon">✨</div>
          <h3 style="font-size: 1.875rem; font-weight: bold; color: white; margin-bottom: 24px;">开始您的专属对话</h3>
          <p style="color: #d1d5db; font-size: 1.125rem; margin-bottom: 32px; line-height: 1.6;">
            选择一个角色，开启一段独一无二的AI伙伴之旅。<br>
            无论是日常闲聊还是深度交流，都能找到心灵相通的感觉。
          </p>
          <button class="cta-btn">立即体验</button>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer cosmic-bg">
    <div class="container">
      <div class="footer-content">
        <div>
          <h3 style="font-size: 1.25rem; font-weight: 600; color: white; margin-bottom: 24px;">关于星野</h3>
          <div style="color: #9ca3af; line-height: 1.6;">
            <p>星野是 MiniMax稀宇科技旗下AI原生应用，</p>
            <p>致力于为用户打造沉浸式AI内容社区。</p>
            <p>于2023年9月上线。</p>
            <p style="margin-top: 16px;">模型名称：abab，备案号：Shanghai-Abab-20230821</p>
          </div>
        </div>
        <div>
          <h3 style="font-size: 1.25rem; font-weight: 600; color: white; margin-bottom: 24px;">联系我们</h3>
          <div class="footer-links">
            <div style="color: #9ca3af; line-height: 1.8;">
              <a href="https://m.xingyeai.com/static/service" target="_blank" class="footer-link">用户协议</a><br>
              <a href="https://m.xingyeai.com/static/privacy" target="_blank" class="footer-link">隐私协议</a><br>
              <p>商务合作：<EMAIL></p>
            </div>
            <div style="color: #9ca3af; line-height: 1.8;">
              <a href="https://beian.miit.gov.cn/" target="_blank" class="footer-link">沪ICP备2025126200号-1</a><br>
              <a href="https://dxzhgl.miit.gov.cn/#/home" target="_blank" class="footer-link">沪B2-20250598</a><br>
              <p>电话：（021）60702590</p>
              <p>不良信息举报：19123091348</p>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>© 2024 星野 - MiniMax稀宇科技. 保留所有权利.</p>
      </div>
    </div>
  </footer>

  <script>
    // Character carousel
    const characters = [
      "https://ext.same-assets.com/3110934667/1038911021.png",
      "https://ext.same-assets.com/3110934667/1704452526.png",
      "https://ext.same-assets.com/3110934667/816326471.png",
      "https://ext.same-assets.com/3110934667/2672004223.png"
    ];
    let currentCharacter = 2;

    function nextCharacter() {
      currentCharacter = (currentCharacter + 1) % characters.length;
      document.getElementById('mainCharacter').src = characters[currentCharacter];
    }

    function prevCharacter() {
      currentCharacter = (currentCharacter - 1 + characters.length) % characters.length;
      document.getElementById('mainCharacter').src = characters[currentCharacter];
    }

    // Feature carousel
    const features = [
      "https://ext.same-assets.com/3110934667/1947432117.png",
      "https://ext.same-assets.com/3110934667/711450851.png",
      "https://ext.same-assets.com/3110934667/2106404189.png",
      "https://ext.same-assets.com/3110934667/2925405737.png",
      "https://ext.same-assets.com/3110934667/150351883.png"
    ];
    let currentFeature = 0;

    function nextFeature() {
      currentFeature = (currentFeature + 1) % features.length;
      updateFeature();
    }

    function prevFeature() {
      currentFeature = (currentFeature - 1 + features.length) % features.length;
      updateFeature();
    }

    function setFeature(index) {
      currentFeature = index;
      updateFeature();
    }

    function updateFeature() {
      document.getElementById('featureImage').src = features[currentFeature];
      const indicators = document.querySelectorAll('.indicator');
      indicators.forEach((indicator, index) => {
        indicator.classList.toggle('active', index === currentFeature);
      });
    }

    // Auto-advance feature carousel
    setInterval(nextFeature, 5000);
  </script>
</body>

</html>