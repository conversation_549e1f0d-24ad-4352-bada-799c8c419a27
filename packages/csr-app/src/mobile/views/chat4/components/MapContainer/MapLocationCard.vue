<template>
  <div
    class="location-card"
    :class="{ locked: location.isLocked && !isUnlocked }"
    :style="cardStyle"
    @click="handleClick"
  >
    <!-- 投影 -->
    <div class="card-shadow"></div>

    <!-- 地点背景图片 -->
    <div
      class="location-image"
      :class="{ locked: location.isLocked && !isUnlocked }"
    >
      <img :src="location.image" :alt="location.name" />

      <!-- 未解锁时的蒙层和锁定图标 -->
      <div v-if="location.isLocked && !isUnlocked" class="lock-overlay">
        <div class="lock-mask"></div>
        <div class="lock-icon-center">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M6 10V8C6 5.79086 7.79086 4 10 4H14C16.2091 4 18 5.79086 18 8V10"
              stroke="#FFFFFF"
              stroke-width="2"
              stroke-linecap="round"
            />
            <rect x="4" y="10" width="16" height="10" rx="2" fill="#FFFFFF" />
            <circle cx="12" cy="15" r="2" fill="#666666" />
          </svg>
        </div>
      </div>
    </div>

    <!-- 位置名称 -->
    <div class="location-name">{{ location.name }}</div>

    <!-- NEW标签 -->
    <div v-if="location.isNew && isUnlocked" class="new-badge">
      <span>NEW</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { MapLocation } from '@/types/map'
import type { FavorabilityState } from '@/types/favorability'
import { SceneUnlockUtils } from '@/types/favorability'

interface Props {
  /** 位置信息 */
  location: MapLocation
  /** 好感度状态 */
  favorabilityState: FavorabilityState
}

interface Emits {
  /** 点击事件 */
  (e: 'click', location: MapLocation): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

/**
 * 检查位置是否解锁
 */
const isUnlocked = computed(() => {
  if (!props.location.isLocked) return true

  const condition = props.location.unlockCondition
  if (!condition) return true

  // 检查好感度等级
  if (condition.requiredLevel) {
    const isLevelUnlocked = SceneUnlockUtils.isLevelUnlocked(
      condition.requiredLevel,
      props.favorabilityState.currentLevel,
    )
    if (!isLevelUnlocked) return false
  }

  // 检查心动值
  if (condition.requiredHeartValue) {
    if (
      props.favorabilityState.currentHeartValue < condition.requiredHeartValue
    ) {
      return false
    }
  }

  return true
})

/**
 * 卡片样式 - 使用百分比定位适配不同屏幕尺寸
 */
const cardStyle = computed(() => {
  const leftValue = props.location.position.x
  const topValue = props.location.position.y

  return {
    left: typeof leftValue === 'number' ? `${leftValue}%` : leftValue,
    top: typeof topValue === 'number' ? `${topValue}%` : topValue,
  }
})

/**
 * 处理点击事件
 */
const handleClick = () => {
  emit('click', props.location)
}
</script>

<style lang="less" scoped>
.location-card {
  position: absolute;
  cursor: pointer;
  transition: transform 0.2s ease;
  z-index: 10;
  width: 44.5vw; // 响应式宽度，约为屏幕宽度的44.5% (167/375)
  max-width: 167px; // 最大宽度限制
  min-width: 140px; // 最小宽度限制
  height: auto; // 自适应高度
  display: flex;
  flex-direction: column; // 垂直排列图片和文字

  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  &.locked {
    opacity: 0.9;
  }
}

.card-shadow {
  position: absolute;
  bottom: -3px;
  left: 0.25px;
  width: 172px;
  height: 3px;
  background: #ded7cf;
  filter: blur(4px);
  z-index: 1;
}

.card-body {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 172px;
  height: 26px;
  z-index: 2;
}

.location-image {
  position: relative;
  width: 100%; // 使用父容器的100%宽度
  height: 0;
  padding-bottom: 56.25%; // 16:9比例 (9/16 * 100%)
  border: 1px solid #ffffff;
  border-radius: 4px;
  overflow: hidden;
  z-index: 3;
  margin-bottom: 2vw; // 响应式间距

  img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }

  &.locked {
    img {
      filter: brightness(0.7); // 锁定时图片变暗
    }
  }
}

.lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 4;
}

.lock-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2); // 半透明黑色蒙层
  border-radius: 4px;
}

.lock-icon-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;

  svg {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  }
}

.card-gradient {
  position: absolute;
  top: 0;
  left: 0.25px;
  width: 172px;
  height: 26px;
  background: linear-gradient(
    90deg,
    rgba(250, 245, 239, 0.26) 0%,
    rgba(255, 234, 214, 1) 35.58%,
    rgba(255, 241, 209, 1) 68.75%,
    rgba(252, 239, 218, 0) 100%
  );
  border-radius: 4px;
}

.top-border {
  position: absolute;
  top: 0;
  left: 0.25px;
  width: 172px;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(227, 248, 190, 0.2) 0%,
    rgba(227, 248, 190, 1) 29.33%,
    rgba(227, 248, 190, 1) 69.71%,
    rgba(227, 248, 190, 0.2) 100%
  );
}

.bottom-border {
  position: absolute;
  bottom: 0;
  left: 0.25px;
  width: 172px;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(216, 189, 174, 0) 0%,
    rgba(216, 189, 174, 1) 50%,
    rgba(216, 189, 174, 0) 100%
  );
}

.location-name {
  width: 100%; // 使用父容器的100%宽度
  height: 6.67vw; // 响应式高度 (25/375 * 100vw)
  max-height: 25px; // 最大高度限制
  min-height: 20px; // 最小高度限制
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 7.47vw; // 响应式内边距 (28/375 * 100vw)
  box-sizing: border-box;
  flex-shrink: 0; // 防止被压缩

  // 简化的背景渐变效果，确保文字可见
  background: linear-gradient(
    90deg,
    rgba(76, 76, 76, 0) 0%,
    rgba(76, 76, 76, 0.8) 50%,
    rgba(76, 76, 76, 0) 100%
  );
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4;
  text-transform: uppercase;
  text-align: center;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8); // 添加文字阴影确保可见性
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.lock-icon {
  position: absolute;
  top: -14px;
  right: -25px;
  width: 54px;
  height: 62px;
  z-index: 20;
}

.lock-background {
  position: relative;
  width: 54px;
  height: 53.66px;
}

.lock-circle {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 50px;
  height: 50px;
}

.circle-blur {
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(24, 34, 108, 0.5);
  border: 0.5px solid #ffffff;
  filter: blur(1px);
}

.circle-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(24, 34, 108, 0.5);
  border: 0.5px solid #ffffff;
}

.circle-inner-border {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 46px;
  height: 46px;
  border-radius: 50%;
  background: rgba(24, 34, 108, 0.5);
  border: 0.5px solid rgba(255, 255, 255, 0.7);
}

.lock-symbol {
  position: absolute;
  top: 21px;
  left: 22px;
  width: 10px;
  height: 11px;
  background: #ffffff;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 0px 2.5px 0px rgba(255, 255, 255, 0.7);
}

.decorative-diamonds {
  position: absolute;
  top: 0;
  left: 0;
  width: 54px;
  height: 53.66px;
}

.diamond-group {
  position: absolute;

  &.top {
    top: 0;
    right: 6px;
    width: 6px;
    height: 53.66px;
  }

  &.bottom {
    bottom: 24.17px;
    left: 0;
    width: 54px;
    height: 5.66px;
  }
}

.diamond {
  position: absolute;
  width: 5.66px;
  height: 5.66px;
  background: #ffffff;
  transform: rotate(45deg);
  filter: blur(1px);

  &:nth-child(1) {
    top: 0;
    left: 0;
  }

  &:nth-child(2) {
    bottom: 0;
    right: 0;
  }
}

.lock-indicator {
  position: absolute;
  bottom: 7px;
  left: 18px;
  width: 18px;
  height: 7px;
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0px 2px 4px 0px rgba(76, 83, 123, 0.2);
  opacity: 0.7;
}

.new-badge {
  position: absolute;
  top: -8px;
  right: -10px;
  padding: 0px 4px;
  height: 10px;
  background: linear-gradient(
    270deg,
    rgba(255, 113, 113, 0) 0%,
    rgba(255, 113, 113, 1) 100%
  );
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 15;

  span {
    font-weight: 500;
    font-size: 8px;
    line-height: 1.4;
    text-transform: uppercase;
    text-align: center;
    color: #ffffff;
    text-shadow: 0px 0px 3px rgba(220, 0, 0, 0.7);
  }
}
</style>
