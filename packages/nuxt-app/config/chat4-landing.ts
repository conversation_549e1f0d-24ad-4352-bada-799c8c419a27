/**
 * Chat4 Landing Page Configuration
 * 配置不同角色的落地页路由和故事信息
 */

export interface Chat4LandingConfig {
  storyId: string
  storyQuote?: string
  characterKey: string // For reporting events (e.g. 'tsunade', 'hancock')
  otherStoryIds: string[]
  showStoryIntro?: boolean
  characterName: string
  characterAvatar?: string
  backgroundImage?: string
  themeColor?: string // 角色专属主题色
}

interface StoryConfig {
  id: string
  quote: string
  characterName?: string
  characterAvatar?: string
  backgroundImage?: string
  themeColor?: string
}

interface Chat4CharacterConfig {
  key: string
  name: string
  storyId: {
    test: string | StoryConfig
    prod: string | StoryConfig
  }
  showStoryIntro?: boolean
  characterAvatar?: string
  backgroundImage?: string
  themeColor?: string // 角色专属主题色
}

// 环境判断
const isTest = process.env.NODE_ENV === 'development'

// Chat4 专用角色配置
export const chat4CharacterConfigs: Chat4CharacterConfig[] = [
  {
    key: 'tsunade',
    name: 'Tsunade',
    storyId: {
      test: {
        id: '5a1af651-4feb-49a4-8262-dee508ebefc8',
        quote: "I'm the Fifth Hokage! Let's have some fun together~",
        characterName: 'Tsunade',
        themeColor: '#ff6b35'
      },
      prod: {
        id: '06770b09-b95b-4e60-8293-d8914d7c0b0b',
        quote: "I'm the Fifth Hokage! Let's have some fun together~",
        characterName: 'Tsunade',
        themeColor: '#ff6b35'
      }
    },
    themeColor: '#ff6b35',
    showStoryIntro: true
  },
  {
    key: 'hancock',
    name: 'Hancock',
    storyId: {
      test: {
        id: '466c25c9-05ba-412c-a9fe-5e47e2c2ad87',
        quote: "I am the most beautiful woman in the world! Worship me~",
        characterName: 'Boa Hancock',
        themeColor: '#e91e63'
      },
      prod: {
        id: '8d12a9ea-0b83-4506-b912-fa85435d3476',
        quote: "I am the most beautiful woman in the world! Worship me~",
        characterName: 'Boa Hancock',
        themeColor: '#e91e63'
      }
    },
    themeColor: '#e91e63',
    showStoryIntro: true
  },
  {
    key: 'nami',
    name: 'Nami',
    storyId: {
      test: {
        id: 'ac49f68d-e995-4a66-8d99-7eb1270adbdb',
        quote: "Navigator of the Straw Hat Pirates! Let's sail together!",
        characterName: 'Nami',
        themeColor: '#ff9800'
      },
      prod: {
        id: '018c2738-2e6a-498f-b0f9-1d027d652c31',
        quote: "Navigator of the Straw Hat Pirates! Let's sail together!",
        characterName: 'Nami',
        themeColor: '#ff9800'
      }
    },
    themeColor: '#ff9800',
    showStoryIntro: true
  },
  {
    key: 'robin',
    name: 'Robin',
    storyId: {
      test: {
        id: '10353a52-4770-4bee-8ae1-181448f1dbb0',
        quote: "Archaeologist with a mysterious past. Want to explore together?",
        characterName: 'Nico Robin',
        themeColor: '#9c27b0'
      },
      prod: {
        id: '78d69bb6-1f20-44dd-9d8c-cc7a92bb70b3',
        quote: "Archaeologist with a mysterious past. Want to explore together?",
        characterName: 'Nico Robin',
        themeColor: '#9c27b0'
      }
    },
    themeColor: '#9c27b0',
    showStoryIntro: true
  },
  {
    key: 'tifa',
    name: 'Tifa',
    storyId: {
      test: {
        id: '6bd1e0ff-e8a3-4238-b832-a09a93057fff',
        quote: "Bartender and fighter from Midgar. Ready for an adventure?",
        characterName: 'Tifa Lockhart',
        themeColor: '#3f51b5'
      },
      prod: {
        id: '9b538d87-2b2c-43a1-be82-22026bdb6c61',
        quote: "Bartender and fighter from Midgar. Ready for an adventure?",
        characterName: 'Tifa Lockhart',
        themeColor: '#3f51b5'
      }
    },
    themeColor: '#3f51b5',
    showStoryIntro: true
  },
  {
    key: 'amy',
    name: 'Amy',
    storyId: {
      test: {
        id: 'ef9986ff-759c-4f09-8b81-bca26d293fde',
        quote: "Sweet and energetic! Let's create beautiful memories together!",
        characterName: 'Amy',
        themeColor: '#e91e63'
      },
      prod: {
        id: 'cdd9d2b7-0c2b-423e-92e2-aa75a888b8f9',
        quote: "Sweet and energetic! Let's create beautiful memories together!",
        characterName: 'Amy',
        themeColor: '#e91e63'
      }
    },
    themeColor: '#e91e63',
    showStoryIntro: true
  }
]

// 获取当前环境的 storyId
const getStoryId = (config: Chat4CharacterConfig) => {
  const storyId = config.storyId[isTest ? 'test' : 'prod']
  return typeof storyId === 'string' ? storyId : storyId.id
}

// 获取故事配置
const getStoryConfig = (config: Chat4CharacterConfig) => {
  const storyConfig = config.storyId[isTest ? 'test' : 'prod']
  return typeof storyConfig === 'string' ? null : storyConfig
}

// 获取所有 storyIds
const getAllStoryIds = () => chat4CharacterConfigs.map((config) => getStoryId(config))

// 生成最终配置
export const chat4LandingConfigs: Record<string, Chat4LandingConfig> = chat4CharacterConfigs.reduce(
  (acc, config, index) => {
    const currentStoryId = getStoryId(config)
    const storyConfig = getStoryConfig(config)
    const allStoryIds = getAllStoryIds()

    acc[config.key] = {
      storyId: currentStoryId,
      storyQuote: storyConfig?.quote,
      characterKey: config.key,
      characterName: storyConfig?.characterName || config.name,
      characterAvatar: storyConfig?.characterAvatar || config.characterAvatar,
      backgroundImage: storyConfig?.backgroundImage || config.backgroundImage,
      themeColor: storyConfig?.themeColor || config.themeColor,
      otherStoryIds: allStoryIds.filter((id) => id !== currentStoryId),
      ...(config.showStoryIntro ? { showStoryIntro: true } : {})
    }

    return acc
  },
  {} as Record<string, Chat4LandingConfig>
)

// 导出配置
export {
  chat4CharacterConfigs,
  chat4LandingConfigs as configs
}

// 获取配置的辅助函数
export const getChat4Config = (characterKey: string): Chat4LandingConfig | null => {
  return chat4LandingConfigs[characterKey] || null
}

// 获取所有可用的角色键
export const getAvailableCharacterKeys = (): string[] => {
  return Object.keys(chat4LandingConfigs)
}

// 获取主要故事（第一个配置的故事）
export const getMainStoryConfig = (): Chat4LandingConfig | null => {
  const firstKey = getAvailableCharacterKeys()[0]
  return firstKey ? getChat4Config(firstKey) : null
}
