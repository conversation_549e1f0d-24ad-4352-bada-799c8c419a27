<template>
  <div
    class="chat4-character-card"
    :style="{ '--character-theme-color': character.themeColor || '#9c88ff' }"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 背景渐变 -->
    <div class="card-background" />

    <!-- 角色图片 -->
    <div class="character-image-container">
      <NuxtImg
        :src="character.characterAvatar || '/images/default-character.jpg'"
        :alt="character.characterName"
        class="character-image"
        loading="lazy"
        format="webp"
      />

      <!-- 心形按钮覆盖层 -->
      <div class="heart-overlay" :class="{ visible: isHovered }">
        <div class="heart-button">
          <Icon name="mdi:heart" />
        </div>
      </div>

      <!-- 状态指示器 -->
      <div class="status-indicator">
        <div class="status-dot live" />
        <span class="status-text">ONLINE</span>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <div class="character-info">
        <h3 class="character-name">{{ character.characterName }}</h3>
        <p class="character-quote">{{
          character.storyQuote || 'Ready to chat!'
        }}</p>
      </div>

      <!-- 交互按钮 -->
      <div class="action-button">
        <span class="button-text">Start Conversation</span>
        <Icon name="mdi:message-heart" class="button-icon" />
      </div>
    </div>

    <!-- 装饰性元素 -->
    <div class="card-decorations">
      <div class="decoration-orb orb-1" />
      <div class="decoration-orb orb-2" />
      <div class="decoration-line" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  character: {
    characterKey: string
    storyId: string
    characterName: string
    storyQuote?: string
    themeColor?: string
    characterAvatar?: string
    backgroundImage?: string
  }
}

interface Emits {
  (e: 'click', character: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式状态
const isHovered = ref(false)

// 方法
const handleClick = () => {
  emit('click', props.character)
}

const handleMouseEnter = () => {
  isHovered.value = true
}

const handleMouseLeave = () => {
  isHovered.value = false
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}
</script>

<style lang="less" scoped>
@import '~/assets/css/chat4.less';

.chat4-character-card {
  position: relative;
  background: var(--chat4-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--chat4-border-color);
  border-radius: var(--chat4-radius-large);
  overflow: hidden;
  cursor: pointer;
  transition: all var(--chat4-transition-normal) ease;
  height: 320px;

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
      var(--chat4-shadow-heavy),
      0 0 30px rgba(255, 0, 77, 0.3);
    border-color: var(--chat4-primary-color);

    .card-background {
      opacity: 1;
    }

    .character-image {
      transform: scale(1.1);
    }

    .decoration-orb {
      animation-play-state: running;
    }

    .decoration-line {
      opacity: 1;
      transform: scaleX(1);
    }
  }
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--chat4-gradient-glass);
  opacity: 0;
  transition: opacity var(--chat4-transition-normal) ease;
  z-index: 1;
}

.character-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
  border-radius: var(--chat4-radius-large) var(--chat4-radius-large) 0 0;
}

.character-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--chat4-transition-slow) ease;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--chat4-transition-normal) ease;

  &.visible {
    opacity: 1;
  }
}

.play-button {
  width: 60px;
  height: 60px;
  background: var(--chat4-gradient-primary);
  border-radius: var(--chat4-radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(0.8);
  transition: transform var(--chat4-transition-normal) ease;

  .play-overlay.visible & {
    transform: scale(1);
  }

  svg {
    width: 24px;
    height: 24px;
    color: white;
    margin-left: 2px;
  }
}

.status-indicator {
  position: absolute;
  top: var(--chat4-spacing-sm);
  right: var(--chat4-spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--chat4-spacing-xs);
  background: rgba(0, 0, 0, 0.7);
  padding: var(--chat4-spacing-xs) var(--chat4-spacing-sm);
  border-radius: var(--chat4-radius-small);
  backdrop-filter: blur(10px);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--chat4-radius-round);

  &.live {
    background: var(--chat4-success-color);
    animation: pulse-dot 2s infinite;
  }
}

.status-text {
  font-size: var(--chat4-font-xs);
  font-weight: 600;
  color: var(--chat4-text-primary);
}

@keyframes pulse-dot {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.card-content {
  position: relative;
  padding: var(--chat4-spacing-md);
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
}

.character-info {
  margin-bottom: var(--chat4-spacing-sm);
}

.character-name {
  font-size: var(--chat4-font-lg);
  font-weight: 700;
  color: var(--chat4-text-primary);
  margin: 0 0 var(--chat4-spacing-xs) 0;
  line-height: 1.2;
}

.story-title {
  font-size: var(--chat4-font-sm);
  color: var(--chat4-text-secondary);
  margin: 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.character-stats {
  display: flex;
  gap: var(--chat4-spacing-md);
  margin-bottom: var(--chat4-spacing-sm);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--chat4-spacing-xs);
}

.stat-icon {
  width: 14px;
  height: 14px;
  color: var(--chat4-accent-color);
}

.stat-value {
  font-size: var(--chat4-font-sm);
  font-weight: 600;
  color: var(--chat4-text-secondary);
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--chat4-spacing-sm) var(--chat4-spacing-md);
  background: var(--chat4-gradient-primary);
  border-radius: var(--chat4-radius-medium);
  transition: all var(--chat4-transition-normal) ease;

  .chat4-character-card:hover & {
    background: var(--chat4-gradient-secondary);
    transform: translateX(4px);
  }
}

.button-text {
  font-size: var(--chat4-font-sm);
  font-weight: 600;
  color: var(--chat4-text-primary);
}

.button-icon {
  width: 16px;
  height: 16px;
  color: var(--chat4-text-primary);
  transition: transform var(--chat4-transition-normal) ease;

  .chat4-character-card:hover & {
    transform: translateX(2px);
  }
}

.card-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.decoration-orb {
  position: absolute;
  border-radius: var(--chat4-radius-round);
  animation: float 6s ease-in-out infinite;
  animation-play-state: paused;

  &.orb-1 {
    width: 20px;
    height: 20px;
    background: radial-gradient(
      circle,
      var(--chat4-primary-color),
      transparent
    );
    top: 20%;
    right: 10%;
    animation-delay: 0s;
  }

  &.orb-2 {
    width: 15px;
    height: 15px;
    background: radial-gradient(
      circle,
      var(--chat4-secondary-color),
      transparent
    );
    bottom: 30%;
    left: 15%;
    animation-delay: 2s;
  }
}

.decoration-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--chat4-gradient-primary);
  opacity: 0;
  transform: scaleX(0);
  transform-origin: left;
  transition: all var(--chat4-transition-normal) ease;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chat4-character-card {
    height: 280px;

    &:hover {
      transform: translateY(-4px) scale(1.01);
    }
  }

  .character-image-container {
    height: 160px;
  }

  .card-content {
    height: 120px;
    padding: var(--chat4-spacing-sm);
  }

  .character-name {
    font-size: var(--chat4-font-md);
  }

  .play-button {
    width: 50px;
    height: 50px;

    svg {
      width: 20px;
      height: 20px;
    }
  }
}
</style>
