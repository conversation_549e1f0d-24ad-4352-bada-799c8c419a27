<template>
  <div class="chat4-particle-background">
    <!-- 主要粒子层 -->
    <div class="particles-layer">
      <div
        v-for="i in particleCount"
        :key="`particle-${i}`"
        class="particle"
        :style="getParticleStyle(i)"
      />
    </div>

    <!-- 浮动光球层 -->
    <div class="orbs-layer">
      <div
        v-for="i in orbCount"
        :key="`orb-${i}`"
        class="floating-orb"
        :style="getOrbStyle(i)"
      />
    </div>

    <!-- 渐变覆盖层 -->
    <div class="gradient-overlays">
      <div class="gradient-overlay gradient-1" />
      <div class="gradient-overlay gradient-2" />
      <div class="gradient-overlay gradient-3" />
    </div>

    <!-- 动态线条 -->
    <div class="lines-layer">
      <div
        v-for="i in lineCount"
        :key="`line-${i}`"
        class="animated-line"
        :style="getLineStyle(i)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// 响应式粒子数量
const particleCount = ref(0)
const orbCount = ref(0)
const lineCount = ref(0)

// 根据屏幕尺寸调整粒子数量
const updateParticleCounts = () => {
  if (import.meta.client) {
    const width = window.innerWidth
    const height = window.innerHeight
    const area = width * height

    // 根据屏幕面积调整粒子数量
    if (width <= 768) {
      // 移动设备 - 减少粒子数量以提高性能
      particleCount.value = Math.min(20, Math.floor(area / 50000))
      orbCount.value = 3
      lineCount.value = 2
    } else if (width <= 1024) {
      // 平板设备
      particleCount.value = Math.min(40, Math.floor(area / 40000))
      orbCount.value = 5
      lineCount.value = 3
    } else {
      // 桌面设备
      particleCount.value = Math.min(60, Math.floor(area / 30000))
      orbCount.value = 7
      lineCount.value = 4
    }
  }
}

// 生成粒子样式
const getParticleStyle = (index: number) => {
  const size = Math.random() * 4 + 2 // 2-6px
  const x = Math.random() * 100
  const y = Math.random() * 100
  const duration = Math.random() * 20 + 10 // 10-30s
  const delay = Math.random() * 5 // 0-5s

  return {
    '--size': `${size}px`,
    '--x': `${x}%`,
    '--y': `${y}%`,
    '--duration': `${duration}s`,
    '--delay': `${delay}s`,
    'left': `${x}%`,
    'top': `${y}%`,
    'width': `${size}px`,
    'height': `${size}px`,
    'animationDuration': `${duration}s`,
    'animationDelay': `${delay}s`,
  }
}

// 生成光球样式
const getOrbStyle = (index: number) => {
  const size = Math.random() * 60 + 40 // 40-100px
  const x = Math.random() * 80 + 10 // 10-90%
  const y = Math.random() * 80 + 10 // 10-90%
  const duration = Math.random() * 15 + 8 // 8-23s
  const delay = Math.random() * 3 // 0-3s

  // 随机选择颜色
  const colors = [
    'var(--chat4-primary-color)',
    'var(--chat4-secondary-color)',
    'var(--chat4-accent-color)',
  ]
  const color = colors[index % colors.length]

  return {
    '--size': `${size}px`,
    '--color': color,
    '--x': `${x}%`,
    '--y': `${y}%`,
    '--duration': `${duration}s`,
    '--delay': `${delay}s`,
    'left': `${x}%`,
    'top': `${y}%`,
    'width': `${size}px`,
    'height': `${size}px`,
    'animationDuration': `${duration}s`,
    'animationDelay': `${delay}s`,
  }
}

// 生成线条样式
const getLineStyle = (index: number) => {
  const width = Math.random() * 200 + 100 // 100-300px
  const height = Math.random() * 2 + 1 // 1-3px
  const x = Math.random() * 100
  const y = Math.random() * 100
  const rotation = Math.random() * 360
  const duration = Math.random() * 10 + 5 // 5-15s
  const delay = Math.random() * 2 // 0-2s

  return {
    '--width': `${width}px`,
    '--height': `${height}px`,
    '--rotation': `${rotation}deg`,
    '--duration': `${duration}s`,
    '--delay': `${delay}s`,
    'left': `${x}%`,
    'top': `${y}%`,
    'width': `${width}px`,
    'height': `${height}px`,
    'transform': `rotate(${rotation}deg)`,
    'animationDuration': `${duration}s`,
    'animationDelay': `${delay}s`,
  }
}

// 监听窗口大小变化
onMounted(() => {
  updateParticleCounts()

  if (import.meta.client) {
    window.addEventListener('resize', updateParticleCounts)
  }
})

onUnmounted(() => {
  if (import.meta.client) {
    window.removeEventListener('resize', updateParticleCounts)
  }
})
</script>

<style lang="less" scoped>
@import '~/assets/css/chat4.less';

.chat4-particle-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.particles-layer {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8), transparent);
  border-radius: 50%;
  animation: particleFloat infinite linear;
  opacity: 0.6;
}

@keyframes particleFloat {
  0% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100vh) translateX(20px) rotate(360deg);
    opacity: 0;
  }
}

.orbs-layer {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-orb {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    var(--color, var(--chat4-primary-color)),
    transparent 70%
  );
  filter: blur(1px);
  animation: orbFloat infinite ease-in-out;
  opacity: 0.3;
}

@keyframes orbFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0.5;
  }
}

.gradient-overlays {
  position: absolute;
  width: 100%;
  height: 100%;
}

.gradient-overlay {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: gradientPulse infinite ease-in-out;

  &.gradient-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(
      circle,
      rgba(156, 136, 255, 0.08),
      // 紫色
      transparent
    );
    top: 20%;
    left: 10%;
    animation-duration: 8s;
    animation-delay: 0s;
  }

  &.gradient-2 {
    width: 400px;
    height: 400px;
    background: radial-gradient(
      circle,
      rgba(255, 158, 181, 0.06),
      // 粉色
      transparent
    );
    bottom: 20%;
    right: 15%;
    animation-duration: 12s;
    animation-delay: 2s;
  }

  &.gradient-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(
      circle,
      rgba(168, 230, 207, 0.05),
      // 薄荷绿
      transparent
    );
    top: 60%;
    left: 60%;
    animation-duration: 10s;
    animation-delay: 4s;
  }
}

@keyframes gradientPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.6;
  }
}

.lines-layer {
  position: absolute;
  width: 100%;
  height: 100%;
}

.animated-line {
  position: absolute;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: lineMove infinite linear;
  opacity: 0.4;
}

@keyframes lineMove {
  0% {
    transform: translateX(-100%) rotate(var(--rotation, 0deg));
    opacity: 0;
  }
  10% {
    opacity: 0.4;
  }
  90% {
    opacity: 0.4;
  }
  100% {
    transform: translateX(100vw) rotate(var(--rotation, 0deg));
    opacity: 0;
  }
}

// 性能优化：减少移动设备上的动画复杂度
@media (max-width: 768px) {
  .particle {
    animation-duration: 15s !important; // 减慢动画速度
  }

  .floating-orb {
    animation-duration: 10s !important;
    filter: blur(2px); // 增加模糊以减少渲染负担
  }

  .gradient-overlay {
    animation-duration: 15s !important;
    filter: blur(60px); // 增加模糊
  }

  .animated-line {
    display: none; // 在移动设备上隐藏线条动画
  }
}

// 尊重用户的动画偏好
@media (prefers-reduced-motion: reduce) {
  .particle,
  .floating-orb,
  .gradient-overlay,
  .animated-line {
    animation: none;
  }

  .chat4-particle-background {
    opacity: 0.5;
  }
}

// 高性能模式：在低端设备上简化动画
@media (max-width: 480px) {
  .particles-layer {
    display: none; // 在小屏幕上隐藏粒子
  }

  .floating-orb {
    animation: none;
    opacity: 0.2;
  }
}
</style>
