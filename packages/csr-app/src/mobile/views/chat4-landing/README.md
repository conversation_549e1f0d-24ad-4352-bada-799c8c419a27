# Chat4 Landing Page

A modern, responsive landing page specifically designed for Chat4 experiences with sophisticated visual effects and interactive character cards.

## Features

### 🎨 Design System
- **Chat4 Color Scheme**: Uses Chat4's signature colors (#ff004d, #4fc3f7, #ffd700)
- **Glass Morphism**: Modern glass effects with backdrop blur
- **Particle Effects**: Animated background particles and floating orbs
- **Responsive Design**: Optimized for both mobile and desktop

### 🎯 Interactive Elements
- **Character Cards**: Clickable cards with hover animations
- **Direct Navigation**: Click any card to launch Chat4 directly
- **Loading States**: Smooth loading indicators and transitions
- **Visual Feedback**: Hover effects and button animations

### 📱 Responsive Layout
- **Mobile**: Single column grid with optimized touch targets
- **Tablet**: 2-column grid with medium-sized cards
- **Desktop**: 3-4 column grid with larger cards and enhanced effects

## Components

### Main Components
- `index.vue` - Main landing page component
- `Chat4CharacterCard.vue` - Individual character card component
- `Chat4ParticleBackground.vue` - Animated background effects

### Key Features
1. **Particle Background**: Animated particles, orbs, and gradient overlays
2. **Character Cards**: Glass morphism cards with character info and stats
3. **Responsive Grid**: Adaptive layout for different screen sizes
4. **Analytics Integration**: Event tracking for user interactions

## Routes

- `/chat4-landing` - Main landing page
- `/chat4-landing/:characterKey` - Character-specific landing page

## Usage

### Navigation
Users can access the Chat4 landing page via:
```
/chat4-landing
```

### Character Selection
Clicking any character card will navigate to:
```
/chat4/{storyId}/{actorId}
```

### Analytics
The landing page tracks the following events:
- Page visits
- Character card clicks
- Story launches

## Styling

The landing page uses Chat4's design system with:
- CSS custom properties for consistent theming
- LESS preprocessing for advanced styling
- Scoped styles to prevent conflicts
- Mobile-first responsive design

## Performance

- **Lazy Loading**: Components are loaded on demand
- **Optimized Animations**: GPU-accelerated animations
- **Responsive Images**: Optimized character images
- **Reduced Motion**: Respects user accessibility preferences

## Browser Support

- Modern browsers with CSS Grid support
- Backdrop-filter support for glass effects
- CSS custom properties support
- ES6+ JavaScript features
