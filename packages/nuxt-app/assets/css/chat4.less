/**
 * Chat4 全局样式
 * 包含Chat4所有组件的通用样式和变量
 */

// Chat4 颜色变量 - 清新女性化风格
:root {
  --chat4-primary-color: #9c88ff; // 主紫色
  --chat4-secondary-color: #ff9eb5; // 粉色
  --chat4-accent-color: #a8e6cf; // 薄荷绿
  --chat4-success-color: #a8e6cf;
  --chat4-warning-color: #ffd93d;
  --chat4-error-color: #ff6b9d;

  // 背景颜色 - 柔和渐变背景
  --chat4-bg-primary: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  --chat4-bg-secondary: rgba(255, 255, 255, 0.9);
  --chat4-bg-overlay: rgba(156, 136, 255, 0.1);
  --chat4-bg-glass: rgba(255, 255, 255, 0.25);
  --chat4-bg-card: rgba(255, 255, 255, 0.8);

  // 文字颜色 - 深色文字适合浅色背景
  --chat4-text-primary: #2d3748;
  --chat4-text-secondary: #4a5568;
  --chat4-text-muted: #718096;
  --chat4-text-light: #ffffff;

  // 边框和阴影 - 柔和阴影
  --chat4-border-color: rgba(156, 136, 255, 0.2);
  --chat4-shadow-light: 0 2px 12px rgba(156, 136, 255, 0.1);
  --chat4-shadow-medium: 0 4px 20px rgba(156, 136, 255, 0.15);
  --chat4-shadow-heavy: 0 8px 32px rgba(156, 136, 255, 0.2);

  // 渐变 - 温柔的渐变色
  --chat4-gradient-primary: linear-gradient(135deg, #9c88ff, #c7a8ff);
  --chat4-gradient-secondary: linear-gradient(135deg, #ff9eb5, #ffc1cc);
  --chat4-gradient-accent: linear-gradient(135deg, #a8e6cf, #c8f7c5);
  --chat4-gradient-glass: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.25),
    rgba(255, 255, 255, 0.1)
  );

  // 动画时长
  --chat4-transition-fast: 0.2s;
  --chat4-transition-normal: 0.3s;
  --chat4-transition-slow: 0.5s;

  // 圆角
  --chat4-radius-small: 8px;
  --chat4-radius-medium: 12px;
  --chat4-radius-large: 20px;
  --chat4-radius-round: 50%;

  // 间距
  --chat4-spacing-xs: 4px;
  --chat4-spacing-sm: 8px;
  --chat4-spacing-md: 16px;
  --chat4-spacing-lg: 24px;
  --chat4-spacing-xl: 32px;

  // 字体大小
  --chat4-font-xs: 10px;
  --chat4-font-sm: 12px;
  --chat4-font-md: 14px;
  --chat4-font-lg: 16px;
  --chat4-font-xl: 18px;
  --chat4-font-2xl: 20px;
  --chat4-font-3xl: 24px;
}

// Chat4 通用样式类
.chat4-glass {
  background: var(--chat4-bg-glass);
  backdrop-filter: blur(10px);
  border: 1px solid var(--chat4-border-color);
}

.chat4-button {
  background: var(--chat4-gradient-primary);
  border: none;
  border-radius: var(--chat4-radius-large);
  color: var(--chat4-text-primary);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--chat4-transition-normal) ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--chat4-shadow-medium);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

.chat4-button-secondary {
  background: var(--chat4-gradient-secondary);
}

.chat4-button-accent {
  background: var(--chat4-gradient-accent);
  color: #333;
}

.chat4-input {
  background: var(--chat4-bg-glass);
  border: 1px solid var(--chat4-border-color);
  border-radius: var(--chat4-radius-medium);
  color: var(--chat4-text-primary);
  padding: var(--chat4-spacing-sm) var(--chat4-spacing-md);
  font-size: var(--chat4-font-md);
  backdrop-filter: blur(10px);

  &::placeholder {
    color: var(--chat4-text-muted);
  }

  &:focus {
    outline: none;
    border-color: var(--chat4-primary-color);
    box-shadow: 0 0 0 2px rgba(255, 0, 77, 0.2);
  }
}

.chat4-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--chat4-bg-overlay);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.chat4-modal-content {
  background: var(--chat4-bg-secondary);
  border-radius: var(--chat4-radius-large);
  padding: var(--chat4-spacing-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--chat4-shadow-heavy);
}

.chat4-avatar {
  border-radius: var(--chat4-radius-round);
  object-fit: cover;
  border: 2px solid var(--chat4-border-color);
}

.chat4-badge {
  background: var(--chat4-bg-glass);
  border-radius: var(--chat4-radius-medium);
  padding: var(--chat4-spacing-xs) var(--chat4-spacing-sm);
  font-size: var(--chat4-font-sm);
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.chat4-progress {
  background: var(--chat4-bg-glass);
  border-radius: var(--chat4-radius-small);
  overflow: hidden;

  .progress-fill {
    height: 100%;
    background: var(--chat4-gradient-primary);
    transition: width var(--chat4-transition-normal) ease;
  }
}

// Chat4 动画类
.chat4-fade-enter-active,
.chat4-fade-leave-active {
  transition: opacity var(--chat4-transition-normal) ease;
}

.chat4-fade-enter-from,
.chat4-fade-leave-to {
  opacity: 0;
}

.chat4-slide-up-enter-active,
.chat4-slide-up-leave-active {
  transition: all var(--chat4-transition-normal) ease;
}

.chat4-slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.chat4-slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.chat4-scale-enter-active,
.chat4-scale-leave-active {
  transition: all var(--chat4-transition-normal) ease;
}

.chat4-scale-enter-from,
.chat4-scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

// Chat4 工具类
.chat4-text-center {
  text-align: center;
}

.chat4-text-left {
  text-align: left;
}

.chat4-text-right {
  text-align: right;
}

.chat4-flex {
  display: flex;
}

.chat4-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat4-flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat4-flex-column {
  display: flex;
  flex-direction: column;
}

.chat4-absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.chat4-full-size {
  width: 100%;
  height: 100%;
}

.chat4-pointer-events-none {
  pointer-events: none;
}

.chat4-pointer-events-auto {
  pointer-events: auto;
}

// 响应式工具类
@media (max-width: 768px) {
  .chat4-mobile-hidden {
    display: none;
  }

  .chat4-modal-content {
    margin: var(--chat4-spacing-md);
    padding: var(--chat4-spacing-lg);
  }
}

@media (min-width: 769px) {
  .chat4-desktop-hidden {
    display: none;
  }
}

// 滚动条样式
.chat4-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--chat4-bg-glass);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--chat4-border-color);
    border-radius: 3px;

    &:hover {
      background: var(--chat4-text-muted);
    }
  }
}

// 选择文本样式
::selection {
  background: var(--chat4-primary-color);
  color: var(--chat4-text-primary);
}
