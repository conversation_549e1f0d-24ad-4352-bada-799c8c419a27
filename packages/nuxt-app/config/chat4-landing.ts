/**
 * Chat4 Landing Page Configuration
 * 配置不同角色的落地页路由和故事信息
 * Chat4 面向女性用户，使用清新温柔的设计风格
 */

export interface Chat4LandingConfig {
  storyId: string
  storyQuote?: string
  characterKey: string // For reporting events (e.g. 'elliot', 'alex')
  otherStoryIds: string[]
  showStoryIntro?: boolean
  characterName: string
  characterAvatar?: string
  backgroundImage?: string
  themeColor?: string // 角色专属主题色（紫色/粉色系）
}

interface StoryConfig {
  id: string
  quote: string
  characterName?: string
  characterAvatar?: string
  backgroundImage?: string
  themeColor?: string
}

interface Chat4CharacterConfig {
  key: string
  name: string
  storyId: {
    test: string | StoryConfig
    prod: string | StoryConfig
  }
  showStoryIntro?: boolean
  characterAvatar?: string
  backgroundImage?: string
  themeColor?: string // 角色专属主题色
}

// 环境判断
const isTest = process.env.NODE_ENV === 'development'

// Chat4 专用男性角色配置（面向女性用户）
export const chat4CharacterConfigs: Chat4CharacterConfig[] = [
  {
    key: 'elliot',
    name: '<PERSON> Vesper',
    storyId: {
      test: {
        id: 'elliot-test-story-id',
        quote:
          'Welcome, welcome! Glad to have you join the haven. Hope you find something useful here.',
        characterName: 'Elliot Vesper',
        themeColor: '#9c88ff',
      },
      prod: {
        id: 'elliot-prod-story-id',
        quote:
          'Welcome, welcome! Glad to have you join the haven. Hope you find something useful here.',
        characterName: 'Elliot Vesper',
        themeColor: '#9c88ff',
      },
    },
    themeColor: '#9c88ff', // 紫色主题
    showStoryIntro: true,
  },
  {
    key: 'alex',
    name: 'Alex Chen',
    storyId: {
      test: {
        id: 'alex-test-story-id',
        quote:
          "Hey there! Ready for an adventure? I promise it'll be worth your time~",
        characterName: 'Alex Chen',
        themeColor: '#ff9eb5',
      },
      prod: {
        id: 'alex-prod-story-id',
        quote:
          "Hey there! Ready for an adventure? I promise it'll be worth your time~",
        characterName: 'Alex Chen',
        themeColor: '#ff9eb5',
      },
    },
    themeColor: '#ff9eb5', // 粉色主题
    showStoryIntro: true,
  },
  {
    key: 'ryan',
    name: 'Ryan Mitchell',
    storyId: {
      test: {
        id: 'ryan-test-story-id',
        quote:
          "Life's too short for boring conversations. Let's make this interesting!",
        characterName: 'Ryan Mitchell',
        themeColor: '#a8e6cf',
      },
      prod: {
        id: 'ryan-prod-story-id',
        quote:
          "Life's too short for boring conversations. Let's make this interesting!",
        characterName: 'Ryan Mitchell',
        themeColor: '#a8e6cf',
      },
    },
    themeColor: '#a8e6cf', // 薄荷绿主题
    showStoryIntro: true,
  },
  {
    key: 'daniel',
    name: 'Daniel Park',
    storyId: {
      test: {
        id: 'daniel-test-story-id',
        quote:
          "Coffee, books, and deep conversations - that's my kind of perfect day.",
        characterName: 'Daniel Park',
        themeColor: '#ffd93d',
      },
      prod: {
        id: 'daniel-prod-story-id',
        quote:
          "Coffee, books, and deep conversations - that's my kind of perfect day.",
        characterName: 'Daniel Park',
        themeColor: '#ffd93d',
      },
    },
    themeColor: '#ffd93d', // 温暖黄色主题
    showStoryIntro: true,
  },
  {
    key: 'lucas',
    name: 'Lucas Rivera',
    storyId: {
      test: {
        id: 'lucas-test-story-id',
        quote:
          'Music is the language of the soul. Want to hear my latest composition?',
        characterName: 'Lucas Rivera',
        themeColor: '#ff6b9d',
      },
      prod: {
        id: 'lucas-prod-story-id',
        quote:
          'Music is the language of the soul. Want to hear my latest composition?',
        characterName: 'Lucas Rivera',
        themeColor: '#ff6b9d',
      },
    },
    themeColor: '#ff6b9d', // 玫瑰粉主题
    showStoryIntro: true,
  },
  {
    key: 'noah',
    name: 'Noah Williams',
    storyId: {
      test: {
        id: 'noah-test-story-id',
        quote:
          'Every sunset is a promise of a new dawn. Care to watch one with me?',
        characterName: 'Noah Williams',
        themeColor: '#c7a8ff',
      },
      prod: {
        id: 'noah-prod-story-id',
        quote:
          'Every sunset is a promise of a new dawn. Care to watch one with me?',
        characterName: 'Noah Williams',
        themeColor: '#c7a8ff',
      },
    },
    themeColor: '#c7a8ff', // 淡紫色主题
    showStoryIntro: true,
  },
]

// 获取当前环境的 storyId
const getStoryId = (config: Chat4CharacterConfig) => {
  const storyId = config.storyId[isTest ? 'test' : 'prod']
  return typeof storyId === 'string' ? storyId : storyId.id
}

// 获取故事配置
const getStoryConfig = (config: Chat4CharacterConfig) => {
  const storyConfig = config.storyId[isTest ? 'test' : 'prod']
  return typeof storyConfig === 'string' ? null : storyConfig
}

// 获取所有 storyIds
const getAllStoryIds = () =>
  chat4CharacterConfigs.map((config) => getStoryId(config))

// 生成最终配置
export const chat4LandingConfigs: Record<string, Chat4LandingConfig> =
  chat4CharacterConfigs.reduce(
    (acc, config, index) => {
      const currentStoryId = getStoryId(config)
      const storyConfig = getStoryConfig(config)
      const allStoryIds = getAllStoryIds()

      acc[config.key] = {
        storyId: currentStoryId,
        storyQuote: storyConfig?.quote,
        characterKey: config.key,
        characterName: storyConfig?.characterName || config.name,
        characterAvatar: storyConfig?.characterAvatar || config.characterAvatar,
        backgroundImage: storyConfig?.backgroundImage || config.backgroundImage,
        themeColor: storyConfig?.themeColor || config.themeColor,
        otherStoryIds: allStoryIds.filter((id) => id !== currentStoryId),
        ...(config.showStoryIntro ? { showStoryIntro: true } : {}),
      }

      return acc
    },
    {} as Record<string, Chat4LandingConfig>,
  )

// 获取配置的辅助函数
export const getChat4Config = (
  characterKey: string,
): Chat4LandingConfig | null => {
  return chat4LandingConfigs[characterKey] || null
}

// 获取所有可用的角色键
export const getAvailableCharacterKeys = (): string[] => {
  return Object.keys(chat4LandingConfigs)
}

// 获取主要故事（第一个配置的故事）
export const getMainStoryConfig = (): Chat4LandingConfig | null => {
  const firstKey = getAvailableCharacterKeys()[0]
  return firstKey ? getChat4Config(firstKey) : null
}
