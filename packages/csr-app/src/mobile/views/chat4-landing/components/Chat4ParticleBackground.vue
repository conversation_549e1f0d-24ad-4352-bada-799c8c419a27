<template>
  <div class="chat4-particle-background">
    <!-- Main Background Gradient -->
    <div class="background-gradient"></div>
    
    <!-- Animated Particles -->
    <div class="particles-container">
      <div
        v-for="i in particleCount"
        :key="i"
        class="particle"
        :style="getParticleStyle(i)"
      ></div>
    </div>
    
    <!-- Floating Orbs -->
    <div class="orbs-container">
      <div
        v-for="i in orbCount"
        :key="i"
        class="floating-orb"
        :style="getOrbStyle(i)"
      ></div>
    </div>
    
    <!-- Gradient Overlays -->
    <div class="gradient-overlay gradient-overlay-1"></div>
    <div class="gradient-overlay gradient-overlay-2"></div>
    <div class="gradient-overlay gradient-overlay-3"></div>
    
    <!-- Animated Lines -->
    <div class="lines-container">
      <div
        v-for="i in lineCount"
        :key="i"
        class="animated-line"
        :style="getLineStyle(i)"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

const particleCount = ref(20)
const orbCount = ref(6)
const lineCount = ref(4)

// Responsive particle counts
const updateParticleCounts = () => {
  const width = window.innerWidth
  if (width < 768) {
    particleCount.value = 12
    orbCount.value = 4
    lineCount.value = 2
  } else if (width < 1024) {
    particleCount.value = 16
    orbCount.value = 5
    lineCount.value = 3
  } else {
    particleCount.value = 20
    orbCount.value = 6
    lineCount.value = 4
  }
}

const getParticleStyle = (index: number) => {
  const delay = (index * 0.3) % 8
  const duration = 6 + (index % 4)
  const size = 2 + (index % 3)
  const startX = (index * 47) % 100
  const startY = (index * 73) % 100
  
  return {
    '--delay': `${delay}s`,
    '--duration': `${duration}s`,
    '--size': `${size}px`,
    '--start-x': `${startX}%`,
    '--start-y': `${startY}%`,
    '--end-x': `${(startX + 20) % 100}%`,
    '--end-y': `${(startY + 30) % 100}%`,
  }
}

const getOrbStyle = (index: number) => {
  const delay = (index * 1.2) % 10
  const duration = 8 + (index % 6)
  const size = 40 + (index % 3) * 20
  const x = (index * 67) % 80 + 10
  const y = (index * 43) % 80 + 10
  const hue = (index * 60) % 360
  
  return {
    '--delay': `${delay}s`,
    '--duration': `${duration}s`,
    '--size': `${size}px`,
    '--x': `${x}%`,
    '--y': `${y}%`,
    '--hue': `${hue}deg`,
  }
}

const getLineStyle = (index: number) => {
  const delay = (index * 2) % 12
  const duration = 10 + (index % 4)
  const angle = (index * 45) % 180
  const length = 100 + (index % 3) * 50
  const x = (index * 25) % 100
  const y = (index * 35) % 100
  
  return {
    '--delay': `${delay}s`,
    '--duration': `${duration}s`,
    '--angle': `${angle}deg`,
    '--length': `${length}px`,
    '--x': `${x}%`,
    '--y': `${y}%`,
  }
}

onMounted(() => {
  updateParticleCounts()
  window.addEventListener('resize', updateParticleCounts)
})
</script>

<style lang="less" scoped>
.chat4-particle-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
  overflow: hidden;
}

.background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    var(--chat4-bg-primary) 0%,
    #1a0033 25%,
    #000 50%,
    #001a33 75%,
    var(--chat4-bg-primary) 100%
  );
}

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: var(--size);
  height: var(--size);
  background: radial-gradient(circle, var(--chat4-primary-color) 0%, transparent 70%);
  border-radius: 50%;
  left: var(--start-x);
  top: var(--start-y);
  animation: particleFloat var(--duration) linear infinite;
  animation-delay: var(--delay);
  opacity: 0.6;
  
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: radial-gradient(circle, var(--chat4-secondary-color) 0%, transparent 50%);
    border-radius: 50%;
    animation: particleGlow calc(var(--duration) * 0.5) ease-in-out infinite alternate;
    animation-delay: var(--delay);
  }
}

.orbs-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.floating-orb {
  position: absolute;
  width: var(--size);
  height: var(--size);
  left: var(--x);
  top: var(--y);
  background: radial-gradient(
    circle,
    hsla(var(--hue), 70%, 60%, 0.3) 0%,
    hsla(var(--hue), 70%, 60%, 0.1) 50%,
    transparent 100%
  );
  border-radius: 50%;
  animation: orbFloat var(--duration) ease-in-out infinite;
  animation-delay: var(--delay);
  filter: blur(1px);
}

.gradient-overlay {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.3;
  animation: gradientPulse 8s ease-in-out infinite;
  
  &.gradient-overlay-1 {
    top: 10%;
    left: 20%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, var(--chat4-primary-color) 0%, transparent 70%);
    animation-delay: 0s;
  }
  
  &.gradient-overlay-2 {
    top: 60%;
    right: 15%;
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, var(--chat4-secondary-color) 0%, transparent 70%);
    animation-delay: 2.5s;
  }
  
  &.gradient-overlay-3 {
    bottom: 20%;
    left: 10%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, var(--chat4-accent-color) 0%, transparent 70%);
    animation-delay: 5s;
  }
}

.lines-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.animated-line {
  position: absolute;
  left: var(--x);
  top: var(--y);
  width: var(--length);
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--chat4-primary-color) 50%,
    transparent 100%
  );
  transform: rotate(var(--angle));
  animation: lineMove var(--duration) linear infinite;
  animation-delay: var(--delay);
  opacity: 0.4;
  
  &::before {
    content: '';
    position: absolute;
    top: -0.5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      var(--chat4-secondary-color) 50%,
      transparent 100%
    );
    filter: blur(1px);
    opacity: 0.5;
  }
}

/* Animations */
@keyframes particleFloat {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translate(
      calc(var(--end-x) - var(--start-x)),
      calc(var(--end-y) - var(--start-y))
    ) scale(0.5);
    opacity: 0;
  }
}

@keyframes particleGlow {
  0% {
    opacity: 0.2;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.5);
  }
}

@keyframes orbFloat {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0.6;
  }
}

@keyframes gradientPulse {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.2);
  }
}

@keyframes lineMove {
  0% {
    transform: rotate(var(--angle)) translateX(-100%);
    opacity: 0;
  }
  10% {
    opacity: 0.4;
  }
  90% {
    opacity: 0.4;
  }
  100% {
    transform: rotate(var(--angle)) translateX(100vw);
    opacity: 0;
  }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .gradient-overlay {
    filter: blur(20px);
    
    &.gradient-overlay-1 {
      width: 200px;
      height: 200px;
    }
    
    &.gradient-overlay-2 {
      width: 150px;
      height: 150px;
    }
    
    &.gradient-overlay-3 {
      width: 120px;
      height: 120px;
    }
  }
  
  .floating-orb {
    filter: blur(0.5px);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .particle,
  .floating-orb,
  .gradient-overlay,
  .animated-line {
    animation-duration: 20s;
    animation-iteration-count: 1;
  }
}
</style>
