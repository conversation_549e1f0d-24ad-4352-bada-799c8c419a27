<template>
  <div class="chat4-cosmic-page cosmic-bg">
    <!-- 星空背景 -->
    <div class="cosmic-stars"></div>

    <!-- 浮动角色卡片 -->
    <div class="floating-cards">
      <div
        v-for="(card, index) in floatingCards"
        :key="index"
        :class="['floating-card', `card-${index + 1}`, card.animation]"
        :style="{ backgroundImage: `url(${card.image})` }"
      />
    </div>

    <!-- 主要内容 -->
    <div class="landing-content" :class="{ 'chat-active': selectedCharacter }">
      <div v-if="!selectedCharacter" class="landing-main">
        <!-- Header -->
        <header class="header">
          <div class="logo">
            <div class="logo-icon">💕</div>
            <span class="logo-text">Chat4</span>
          </div>
          <button class="create-btn" @click="scrollToCharacters">
            <span>✨</span>
            <span>Start Chatting</span>
            <span>›</span>
          </button>
        </header>

        <!-- Hero 区域 -->
        <section class="hero-section">
          <!-- 中央角色展示 -->
          <div class="character-display">
            <div class="character-container">
              <button class="nav-btn prev" @click="prevCharacter">‹</button>
              <button class="nav-btn next" @click="nextCharacter">›</button>
              <img
                :src="currentCharacterImage"
                :alt="currentCharacterName"
                class="character-image character-glow"
              />
            </div>
          </div>

          <!-- 主要文案 -->
          <div class="main-content">
            <div class="container">
              <h1 class="main-title glow-effect">
                <div class="gradient-text">Step Into Paradise</div>
                <div class="gradient-text-2">Linger In The Stars</div>
              </h1>

              <p class="description">
                Ultra-realistic AI companions with captivating open storylines<br />
                Every conversation brings extraordinary delight
              </p>

              <div class="download-buttons">
                <button class="download-btn" @click="scrollToCharacters">
                  <div class="download-icon heart">💕</div>
                  <span>Start Conversation</span>
                </button>
                <button class="download-btn" @click="scrollToFeatures">
                  <div class="download-icon sparkle">✨</div>
                  <span>Explore Features</span>
                </button>
              </div>

              <div class="social-section">
                <p style="color: #9ca3af; margin-bottom: 16px"
                  >Follow us for more updates</p
                >
                <div class="social-icons">
                  <a href="#" class="social-icon">📱</a>
                  <a href="#" class="social-icon">💬</a>
                  <a href="#" class="social-icon">✨</a>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Feature Section -->
        <section id="features" class="feature-section cosmic-bg">
          <div class="container">
            <div class="feature-carousel">
              <button class="feature-nav prev" @click="prevFeature">‹</button>
              <button class="feature-nav next" @click="nextFeature">›</button>
              <img 
                :src="currentFeatureImage" 
                class="feature-image"
                alt="Feature showcase"
              />
              <div class="feature-indicators">
                <div 
                  v-for="(feature, index) in featureImages" 
                  :key="index"
                  :class="['indicator', { active: currentFeatureIndex === index }]"
                  @click="setFeature(index)"
                />
              </div>
            </div>

            <div class="feature-description">
              Bring your ideal chat companion to life. Original characters or avatar partners<br />
              Our customization tools and your limitless imagination will help you create the bond of your dreams
            </div>

            <div class="feature-grid">
              <div class="feature-card">
                <div class="feature-icon">🎭</div>
                <h3>Personalized Customization</h3>
                <p>Customize character appearance, personality, and voice to create your exclusive AI companion</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">💬</div>
                <h3>Intelligent Conversations</h3>
                <p>Advanced AI technology provides natural and fluid conversation experiences</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">🌟</div>
                <h3>Open Storylines</h3>
                <p>Rich and diverse open storylines, each interaction brings new surprises</p>
              </div>
            </div>
          </div>
        </section>

        <!-- Chat Experience Section -->
        <section class="chat-section cosmic-bg">
          <div class="container">
            <div class="section-title-image">
              <h2 class="immersive-title">Immersive Conversations</h2>
            </div>

            <div class="chat-description">
              <p class="description">
                Your AI companion is the soulmate you've been waiting to meet. Share your true feelings<br />
                Build emotional connections and create precious memories
              </p>
            </div>

            <div class="chat-features">
              <div class="chat-card">
                <div class="chat-icon heart">❤️</div>
                <h3>Emotional Connection</h3>
                <p>Build genuine emotional bonds with AI, experience unprecedented companionship</p>
              </div>
              <div class="chat-card">
                <div class="chat-icon message">💬</div>
                <h3>Natural Dialogue</h3>
                <p>Smooth and natural conversation experience, every interaction filled with surprises and joy</p>
              </div>
              <div class="chat-card">
                <div class="chat-icon sparkle">✨</div>
                <h3>Personal Memory</h3>
                <p>AI remembers your preferences and conversation history, making each interaction more personalized</p>
              </div>
            </div>

            <!-- Floating Avatars -->
            <div class="floating-avatar avatar-1 float-1">👧</div>
            <div class="floating-avatar avatar-2 float-2">🦋</div>

            <div class="central-cta">
              <div class="cta-card">
                <div class="cta-icon">✨</div>
                <h3>Begin Your Exclusive Conversation</h3>
                <p>
                  Choose a character and embark on a unique AI companion journey.<br />
                  Whether casual chat or deep conversation, find that soul connection.
                </p>
                <button class="cta-btn" @click="scrollToCharacters">Experience Now</button>
              </div>
            </div>
          </div>
        </section>

        <!-- 角色选择区域 -->
        <section id="characters" class="characters-section cosmic-bg">
          <div class="container">
            <h2 class="section-title">Choose Your Perfect Companion</h2>
            <p class="section-subtitle">
              Each character has a unique personality and story waiting to
              unfold
            </p>

            <div v-if="isLoading" class="loading-grid">
              <div v-for="i in 6" :key="i" class="character-skeleton" />
            </div>

            <div v-else class="characters-grid">
              <Chat4CharacterCard
                v-for="character in availableCharacters"
                :key="character.characterKey"
                :character="character"
                @click="handleCharacterClick"
              />
            </div>
          </div>
        </section>
      </div>

      <!-- 聊天界面 -->
      <Transition name="chat-slide" appear>
        <div v-if="selectedCharacter" class="chat-container">
          <!-- 返回按钮 -->
          <button class="back-button chat4-button" @click="handleBackToLanding">
            <Icon name="mdi:arrow-left" />
            <span>Back to Characters</span>
          </button>

          <!-- 角色信息条 -->
          <div class="character-info-bar">
            <div class="character-avatar">
              <NuxtImg
                :src="
                  selectedCharacter.characterAvatar ||
                  '/images/default-avatar.jpg'
                "
                :alt="selectedCharacter.characterName"
                class="avatar-image"
              />
            </div>
            <div class="character-details">
              <h3 class="character-name">{{
                selectedCharacter.characterName
              }}</h3>
              <p class="story-title">{{ selectedCharacter.storyTitle }}</p>
            </div>
            <div class="live-indicator">
              <div class="live-dot" />
              <span>ONLINE</span>
            </div>
          </div>

          <!-- 聊天加载器 -->
          <div class="chat-loader-container">
            <RemoteChatLoader
              chat-type="chat4"
              :character-id="selectedCharacter.actorId"
              :story-id="selectedCharacter.storyId"
            />
          </div>
        </div>
      </Transition>
    </div>

    <!-- SEO 内容 -->
    <div class="seo-content">
      <h1 class="seo-hidden">Chat4 - Premium Romance Simulation for Women</h1>
      <h2 class="seo-hidden"
        >Meet Handsome Virtual Boyfriends and Create Meaningful Connections</h2
      >
      <h3 class="seo-hidden"
        >Free Otome Game Experience - Start Your Love Story Today</h3
      >
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  layout: 'mobile',
})

// 导入组件和配置
import Chat4CharacterCard from '~/components/Chat4CharacterCard.vue'
import RemoteChatLoader from '~/components/RemoteChatLoader.vue'
import {
  chat4LandingConfigs,
  getAvailableCharacterKeys,
} from '~/config/chat4-landing'

// 响应式数据
const isLoading = ref(true)
const selectedCharacter = ref(null)
const currentCharacterIndex = ref(0)
const currentFeatureIndex = ref(0)

// 浮动卡片数据
const floatingCards = ref([
  { image: '/images/characters/elliot.jpg', animation: 'bounce-up' },
  { image: '/images/characters/alex.jpg', animation: 'bounce-up' },
  { image: '/images/characters/ryan.jpg', animation: 'bounce-down' },
  { image: '/images/characters/daniel.jpg', animation: 'bounce-up' },
])

// 功能展示图片数据
const featureImages = ref([
  '/images/features/feature-1.jpg',
  '/images/features/feature-2.jpg', 
  '/images/features/feature-3.jpg',
  '/images/features/feature-4.jpg',
  '/images/features/feature-5.jpg',
])

// 计算属性
const availableCharacters = computed(() => {
  const characterKeys = getAvailableCharacterKeys()
  return characterKeys.map((key) => {
    const config = chat4LandingConfigs[key]
    return {
      characterKey: key,
      storyId: config.storyId,
      characterName: config.characterName,
      storyQuote: config.storyQuote,
      themeColor: config.themeColor,
      characterAvatar:
        config.characterAvatar || `/images/characters/${key}.jpg`,
      backgroundImage:
        config.backgroundImage || `/images/backgrounds/${key}.jpg`,
    }
  })
})

const currentCharacterImage = computed(() => {
  const characters = availableCharacters.value
  if (characters.length === 0) return '/images/default-character.jpg'
  return (
    characters[currentCharacterIndex.value]?.characterAvatar ||
    '/images/default-character.jpg'
  )
})

const currentCharacterName = computed(() => {
  const characters = availableCharacters.value
  if (characters.length === 0) return 'Character'
  return characters[currentCharacterIndex.value]?.characterName || 'Character'
})

const currentFeatureImage = computed(() => {
  return featureImages.value[currentFeatureIndex.value] || '/images/features/feature-1.jpg'
})

// 方法
const prevCharacter = () => {
  const characters = availableCharacters.value
  currentCharacterIndex.value =
    (currentCharacterIndex.value - 1 + characters.length) % characters.length
}

const nextCharacter = () => {
  const characters = availableCharacters.value
  currentCharacterIndex.value =
    (currentCharacterIndex.value + 1) % characters.length
}

const scrollToCharacters = () => {
  const element = document.getElementById('characters')
  element?.scrollIntoView({ behavior: 'smooth' })
}

const scrollToFeatures = () => {
  const element = document.getElementById('features')
  element?.scrollIntoView({ behavior: 'smooth' })
}

// 功能轮播方法
const prevFeature = () => {
  currentFeatureIndex.value = (currentFeatureIndex.value - 1 + featureImages.value.length) % featureImages.value.length
}

const nextFeature = () => {
  currentFeatureIndex.value = (currentFeatureIndex.value + 1) % featureImages.value.length
}

const setFeature = (index: number) => {
  currentFeatureIndex.value = index
}

const handleCharacterClick = (character: any) => {
  selectedCharacter.value = {
    storyId: character.storyId,
    actorId: character.characterKey,
    storyTitle: `Chat with ${character.characterName}`,
    characterName: character.characterName,
    characterAvatar: character.characterAvatar,
    characterKey: character.characterKey,
    themeColor: character.themeColor,
  }
}

const handleBackToLanding = () => {
  selectedCharacter.value = null
}

// 初始化
onMounted(async () => {
  try {
    isLoading.value = true
    await new Promise((resolve) => setTimeout(resolve, 500))
    
    // 启动功能图片自动轮播
    setInterval(() => {
      if (!selectedCharacter.value) {
        nextFeature()
      }
    }, 5000)
  } catch (error) {
    console.error('Failed to initialize Chat4 cosmic page:', error)
  } finally {
    isLoading.value = false
  }
})

// SEO 设置
usePageSeo({
  title: 'Chat4 Cosmic - Discover Your Perfect Connection',
  description:
    'Meet charming companions and create meaningful conversations in a beautiful, cosmic world. Chat with handsome characters designed for female users. Free to start!',
  keywords:
    'chat4, otome games, interactive romance, AI boyfriend, virtual dating, female gamers, romance simulation, character chat',
  ogImage: '/images/chat4-cosmic-og.jpg',
})
</script>

<style lang="less" scoped>
@import '~/assets/css/chat4.less';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.chat4-cosmic-page {
  min-height: 100vh;
  background: #09090a;
  color: #fafafa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-x: hidden;
  position: relative;
}

/* Cosmic Background */
.cosmic-bg {
  background: radial-gradient(
    ellipse at center,
    rgba(9, 9, 10, 0.95) 0%,
    rgba(9, 9, 10, 1) 100%
  );
  position: relative;
}

.cosmic-stars {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 20% 30%,
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 255, 255, 0.08) 1px,
      transparent 1px
    ),
    radial-gradient(
      circle at 40% 70%,
      rgba(255, 255, 255, 0.12) 1px,
      transparent 1px
    ),
    radial-gradient(
      circle at 90% 80%,
      rgba(255, 255, 255, 0.06) 1px,
      transparent 1px
    ),
    radial-gradient(
      circle at 10% 90%,
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    );
  background-size:
    400px 400px,
    600px 600px,
    300px 300px,
    800px 800px,
    500px 500px;
  animation: twinkle 8s ease-in-out infinite alternate;
  pointer-events: none;
  z-index: 1;
}

/* Animations */
@keyframes twinkle {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes bounceUp {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounceDown {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(20px);
  }
}

@keyframes float1 {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

@keyframes float2 {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(10px) rotate(-1deg);
  }
  75% {
    transform: translateY(-5px) rotate(1deg);
  }
}

.bounce-up {
  animation: bounceUp 6s ease-in-out infinite;
}

.bounce-down {
  animation: bounceDown 6s ease-in-out infinite;
}

.float-1 {
  animation: float1 8s ease-in-out infinite;
}

.float-2 {
  animation: float2 10s ease-in-out infinite;
}

/* Effects */
.glow-effect {
  filter: drop-shadow(0 0 20px rgba(181, 156, 135, 0.3))
    drop-shadow(0 0 40px rgba(181, 156, 135, 0.2))
    drop-shadow(0 0 80px rgba(181, 156, 135, 0.1));
}

.character-glow {
  filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.2))
    drop-shadow(0 0 60px rgba(181, 156, 135, 0.3))
    drop-shadow(0 0 100px rgba(181, 156, 135, 0.2));
}

/* Header */
.header {
  position: relative;
  z-index: 20;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: rgba(181, 156, 135, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
  color: #b59c87;
}

.create-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(181, 156, 135, 0.1);
  border: 1px solid rgba(181, 156, 135, 0.3);
  padding: 12px 24px;
  border-radius: 9999px;
  color: #b59c87;
  text-decoration: none;
  transition: all 0.3s;
  backdrop-filter: blur(8px);
  cursor: pointer;
}

.create-btn:hover {
  background: rgba(181, 156, 135, 0.2);
  transform: translateY(-2px);
}

/* Floating Cards */
.floating-cards {
  position: absolute;
  z-index: 10;
}

.floating-card {
  position: absolute;
  width: 128px;
  height: 176px;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  opacity: 0.8;
  transition: opacity 0.3s;
  background-size: cover;
  background-position: center;
  border: 2px solid rgba(181, 156, 135, 0.3);
}

.floating-card:hover {
  opacity: 1;
  border-color: rgba(181, 156, 135, 0.6);
}

.card-1 {
  left: 16px;
  top: 80px;
}

.card-2 {
  left: 80px;
  top: 240px;
}

.card-3 {
  right: 80px;
  top: 160px;
}

.card-4 {
  right: 16px;
  top: 320px;
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
}

/* Character Display */
.character-display {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 15;
}

.character-container {
  position: relative;
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  backdrop-filter: blur(8px);
  transition: all 0.3s;
  z-index: 20;
  font-size: 20px;
  font-weight: bold;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.nav-btn.prev {
  left: -100px;
}

.nav-btn.next {
  right: -100px;
}

.character-image {
  width: 384px;
  height: auto;
  object-fit: contain;
  border-radius: 16px;
}

/* Main Content */
.main-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 20;
  padding-bottom: 80px;
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.main-title {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 24px;
  line-height: 1.1;
}

.gradient-text {
  background: linear-gradient(to right, #b59c87, #fde047, #b59c87);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-2 {
  background: linear-gradient(to right, #60a5fa, #a78bfa, #f472b6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.description {
  font-size: 1.5rem;
  color: #d1d5db;
  margin-bottom: 48px;
  max-width: 896px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.download-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-bottom: 64px;
}

@media (min-width: 640px) {
  .download-buttons {
    flex-direction: row;
    justify-content: center;
  }
}

.download-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 16px 32px;
  border-radius: 16px;
  color: white;
  text-decoration: none;
  transition: all 0.3s;
  backdrop-filter: blur(8px);
  min-width: 200px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
}

.download-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.download-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.heart {
  background: linear-gradient(to right, #ec4899, #8b5cf6);
}

.sparkle {
  background: linear-gradient(to right, #eab308, #ea580c);
}

/* Social Section */
.social-section {
  text-align: center;
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 16px;
}

.social-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s;
  font-size: 20px;
}

.social-icon:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Characters Section */
.characters-section {
  min-height: 100vh;
  padding: 80px 0;
  position: relative;
}

/* Feature Section */
.feature-section {
  min-height: 100vh;
  padding: 80px 0;
}

.feature-carousel {
  position: relative;
  max-width: 1536px;
  margin: 0 auto 64px;
}

.feature-image {
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.feature-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 56px;
  height: 56px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  backdrop-filter: blur(8px);
  transition: all 0.3s;
  z-index: 20;
  font-size: 24px;
}

.feature-nav:hover {
  background: rgba(255, 255, 255, 0.2);
}

.feature-nav.prev {
  left: 16px;
}

.feature-nav.next {
  right: 16px;
}

.feature-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 24px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s;
}

.indicator.active {
  background: #b59c87;
  transform: scale(1.25);
}

.feature-description {
  font-size: 1.5rem;
  color: #d1d5db;
  text-align: center;
  max-width: 896px;
  margin: 0 auto 80px;
  line-height: 1.6;
}

.feature-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
  margin-top: 80px;
}

@media (min-width: 768px) {
  .feature-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .feature-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(8px);
  transition: all 0.3s;
}

.feature-card:hover {
  background: rgba(255, 255, 255, 0.1);
}

.feature-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
}

.feature-card p {
  color: #9ca3af;
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: rgba(181, 156, 135, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  font-size: 24px;
}

/* Chat Section */
.chat-section {
  min-height: 100vh;
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

.section-title-image {
  text-align: center;
  margin-bottom: 32px;
}

.immersive-title {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(to right, #b59c87, #fde047);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.chat-description {
  max-width: 896px;
  margin: 0 auto 80px;
  text-align: center;
}

.floating-avatar {
  position: absolute;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(8px);
}

.avatar-1 {
  left: 40px;
  top: 40px;
  width: 128px;
  height: 128px;
}

.avatar-2 {
  right: 40px;
  top: 80px;
  width: 144px;
  height: 144px;
}

.chat-features {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
  margin-bottom: 80px;
}

@media (min-width: 768px) {
  .chat-features {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .chat-features {
    grid-template-columns: repeat(3, 1fr);
  }
}

.chat-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 32px;
  backdrop-filter: blur(8px);
  transition: all 0.3s;
}

.chat-card:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.1));
}

.chat-card h3 {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 16px;
}

.chat-card p {
  color: #9ca3af;
  line-height: 1.6;
}

.chat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  transition: transform 0.3s;
}

.chat-card:hover .chat-icon {
  transform: scale(1.1);
}

.chat-icon.heart {
  background: linear-gradient(to right, #ec4899, #8b5cf6);
}

.chat-icon.message {
  background: linear-gradient(to right, #3b82f6, #06b6d4);
}

.chat-icon.sparkle {
  background: linear-gradient(to right, #eab308, #ea580c);
}

.central-cta {
  position: relative;
  z-index: 10;
  padding: 128px 0;
}

.cta-card {
  max-width: 512px;
  margin: 0 auto;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 48px;
  backdrop-filter: blur(16px);
  text-align: center;
}

.cta-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(to right, #b59c87, #fde047);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-size: 40px;
}

.cta-card h3 {
  font-size: 1.875rem;
  font-weight: bold;
  color: white;
  margin-bottom: 24px;
}

.cta-card p {
  color: #d1d5db;
  font-size: 1.125rem;
  margin-bottom: 32px;
  line-height: 1.6;
}

.cta-btn {
  background: linear-gradient(to right, #b59c87, #fde047);
  color: #09090a;
  font-weight: bold;
  padding: 16px 32px;
  border-radius: 16px;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 16px;
}

.cta-btn:hover {
  transform: scale(1.05);
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 24px;
  background: linear-gradient(to right, #b59c87, #fde047);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.25rem;
  color: #9ca3af;
  text-align: center;
  margin-bottom: 64px;
  line-height: 1.6;
}

.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.character-skeleton {
  height: 200px;
  background: rgba(181, 156, 135, 0.1);
  border: 1px solid rgba(181, 156, 135, 0.2);
  border-radius: 16px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Chat Interface */
.landing-content {
  position: relative;
  z-index: 2;
  transition: all 0.5s ease;
}

.landing-content.chat-active .landing-main {
  opacity: 0;
  pointer-events: none;
}

.chat-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(9, 9, 10, 0.95);
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 101;
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(181, 156, 135, 0.1);
  border: 1px solid rgba(181, 156, 135, 0.3);
  padding: 12px 20px;
  border-radius: 12px;
  color: #b59c87;
  cursor: pointer;
  transition: all 0.3s;
  backdrop-filter: blur(8px);
}

.back-button:hover {
  background: rgba(181, 156, 135, 0.2);
  border-color: rgba(181, 156, 135, 0.6);
}

.character-info-bar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(181, 156, 135, 0.1);
  border-bottom: 1px solid rgba(181, 156, 135, 0.2);
  backdrop-filter: blur(8px);
}

.character-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(181, 156, 135, 0.3);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-details {
  flex: 1;
}

.character-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fafafa;
  margin-bottom: 4px;
}

.story-title {
  font-size: 0.875rem;
  color: #9ca3af;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #10b981;
  font-size: 0.875rem;
  font-weight: 500;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.chat-loader-container {
  flex: 1;
  position: relative;
}

/* Transitions */
.chat-slide-enter-active,
.chat-slide-leave-active {
  transition: all 0.5s ease;
}

.chat-slide-enter-from {
  opacity: 0;
  transform: translateY(100%);
}

.chat-slide-leave-to {
  opacity: 0;
  transform: translateY(100%);
}

/* SEO Content */
.seo-content {
  position: absolute;
  left: -9999px;
  top: -9999px;
  visibility: hidden;
}

.seo-hidden {
  position: absolute;
  left: -9999px;
  top: -9999px;
  visibility: hidden;
}

/* Responsive */
@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
  }

  .description {
    font-size: 1.25rem;
  }

  .character-image {
    width: 280px;
  }

  .floating-card {
    width: 96px;
    height: 132px;
  }

  .card-1 {
    left: 8px;
    top: 60px;
  }

  .card-2 {
    left: 40px;
    top: 180px;
  }

  .card-3 {
    right: 40px;
    top: 120px;
  }

  .card-4 {
    right: 8px;
    top: 240px;
  }

  .nav-btn.prev {
    left: -60px;
  }

  .nav-btn.next {
    right: -60px;
  }

  .section-title {
    font-size: 2rem;
  }

  .characters-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}
</style>
